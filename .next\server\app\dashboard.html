<!DOCTYPE html><!--uMSIbYafsYeq_W1FcyE44--><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/9607e0db544c62ae.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-3c65a00fa6593823.js"/><script src="/_next/static/chunks/4bd1b696-cf72ae8a39fa05aa.js" async=""></script><script src="/_next/static/chunks/964-02efbd2195ef91bd.js" async=""></script><script src="/_next/static/chunks/main-app-27819976d82807e8.js" async=""></script><script src="/_next/static/chunks/493-9183a606786a8e5a.js" async=""></script><script src="/_next/static/chunks/app/layout-b91f0cb18d2a387d.js" async=""></script><script src="/_next/static/chunks/277-6cd9c283f70fe2a1.js" async=""></script><script src="/_next/static/chunks/491-945cd5538409c224.js" async=""></script><script src="/_next/static/chunks/357-d104fcae1d10ae74.js" async=""></script><script src="/_next/static/chunks/app/(dashboard)/layout-71e46c4c83e575b3.js" async=""></script><title>QRSAMS - QR-Code Based Student Attendance and Monitoring System</title><meta name="description" content="Tanauan School of Arts and Trade - Student Attendance Management System"/><link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16"/><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__variable_5cfdac __variable_9a8899 antialiased"><div hidden=""><!--$--><!--/$--></div><script>((a,b,c,d,e,f,g,h)=>{let i=document.documentElement,j=["light","dark"];function k(b){var c;(Array.isArray(a)?a:[a]).forEach(a=>{let c="class"===a,d=c&&f?e.map(a=>f[a]||a):e;c?(i.classList.remove(...d),i.classList.add(f&&f[b]?f[b]:b)):i.setAttribute(a,b)}),c=b,h&&j.includes(c)&&(i.style.colorScheme=c)}if(d)k(d);else try{let a=localStorage.getItem(b)||c,d=g&&"system"===a?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":a;k(d)}catch(a){}})("class","theme","system",null,["light","dark"],null,true,true)</script><div class="flex h-screen bg-background"><aside class="hidden md:block"><div class="flex h-full w-64 flex-col bg-background border-r"><div class="flex items-center gap-3 p-6 border-b"><div class="h-8 w-8 rounded-lg bg-primary/10 flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-graduation-cap h-5 w-5 text-primary" aria-hidden="true"><path d="M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z"></path><path d="M22 10v6"></path><path d="M6 12.5V16a6 3 0 0 0 12 0v-3.5"></path></svg></div><div><h2 class="text-lg font-semibold">QRSAMS</h2><p class="text-xs text-muted-foreground">Tanauan School</p></div></div><nav class="flex-1 p-4 space-y-2"><a data-slot="button" class="inline-flex items-center whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive text-secondary-foreground shadow-xs hover:bg-secondary/80 px-4 py-2 has-[&gt;svg]:px-3 w-full justify-start gap-3 h-11 bg-secondary" href="/dashboard"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-layout-dashboard h-4 w-4" aria-hidden="true"><rect width="7" height="9" x="3" y="3" rx="1"></rect><rect width="7" height="5" x="14" y="3" rx="1"></rect><rect width="7" height="9" x="14" y="12" rx="1"></rect><rect width="7" height="5" x="3" y="16" rx="1"></rect></svg>Dashboard</a><a data-slot="button" class="inline-flex items-center whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50 px-4 py-2 has-[&gt;svg]:px-3 w-full justify-start gap-3 h-11" href="/students"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-users h-4 w-4" aria-hidden="true"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><path d="M16 3.128a4 4 0 0 1 0 7.744"></path><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><circle cx="9" cy="7" r="4"></circle></svg>Students</a><a data-slot="button" class="inline-flex items-center whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50 px-4 py-2 has-[&gt;svg]:px-3 w-full justify-start gap-3 h-11" href="/attendance"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clipboard-check h-4 w-4" aria-hidden="true"><rect width="8" height="4" x="8" y="2" rx="1" ry="1"></rect><path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path><path d="m9 14 2 2 4-4"></path></svg>Attendance</a><a data-slot="button" class="inline-flex items-center whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50 px-4 py-2 has-[&gt;svg]:px-3 w-full justify-start gap-3 h-11" href="/reports"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-text h-4 w-4" aria-hidden="true"><path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path><path d="M10 9H8"></path><path d="M16 13H8"></path><path d="M16 17H8"></path></svg>Reports</a><a data-slot="button" class="inline-flex items-center whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50 px-4 py-2 has-[&gt;svg]:px-3 w-full justify-start gap-3 h-11" href="/analytics"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chart-column h-4 w-4" aria-hidden="true"><path d="M3 3v16a2 2 0 0 0 2 2h16"></path><path d="M18 17V9"></path><path d="M13 17V5"></path><path d="M8 17v-3"></path></svg>Analytics</a><a data-slot="button" class="inline-flex items-center whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50 px-4 py-2 has-[&gt;svg]:px-3 w-full justify-start gap-3 h-11" href="/scanner"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-qr-code h-4 w-4" aria-hidden="true"><rect width="5" height="5" x="3" y="3" rx="1"></rect><rect width="5" height="5" x="16" y="3" rx="1"></rect><rect width="5" height="5" x="3" y="16" rx="1"></rect><path d="M21 16h-3a2 2 0 0 0-2 2v3"></path><path d="M21 21v.01"></path><path d="M12 7v3a2 2 0 0 1-2 2H7"></path><path d="M3 12h.01"></path><path d="M12 3h.01"></path><path d="M12 16v.01"></path><path d="M16 12h1"></path><path d="M21 12v.01"></path><path d="M12 21v-1"></path></svg>Scanner</a></nav><div class="p-4 border-t"><div class="text-center"><p class="text-xs text-muted-foreground">Tanauan School of Arts and Trade</p><p class="text-xs text-muted-foreground">Brgy. Cabuynan, Tanauan, Leyte</p></div></div></div></aside><div class="flex-1 flex flex-col overflow-hidden"><header class="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60"><div class="container flex h-16 items-center justify-between px-4"><div class="flex items-center gap-4 md:hidden"><button data-slot="sheet-trigger" class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50 size-9" type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="radix-_R_2qbtb_" data-state="closed"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu h-5 w-5" aria-hidden="true"><path d="M4 12h16"></path><path d="M4 18h16"></path><path d="M4 6h16"></path></svg></button></div><div class="flex items-center gap-3"><div class="h-10 w-10 rounded-lg bg-primary/10 flex items-center justify-center"><div class="h-6 w-6 rounded bg-primary"></div></div><div class="hidden sm:block"><h1 class="text-lg font-semibold">QRSAMS</h1><p class="text-xs text-muted-foreground">Tanauan School of Arts and Trade</p></div></div><div class="flex items-center gap-2"><button data-slot="button" class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50 size-9"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-sun h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" aria-hidden="true"><circle cx="12" cy="12" r="4"></circle><path d="M12 2v2"></path><path d="M12 20v2"></path><path d="m4.93 4.93 1.41 1.41"></path><path d="m17.66 17.66 1.41 1.41"></path><path d="M2 12h2"></path><path d="M20 12h2"></path><path d="m6.34 17.66-1.41 1.41"></path><path d="m19.07 4.93-1.41 1.41"></path></svg><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-moon absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" aria-hidden="true"><path d="M20.985 12.486a9 9 0 1 1-9.473-9.472c.405-.022.617.46.402.803a6 6 0 0 0 8.268 8.268c.344-.215.825-.004.803.401"></path></svg><span class="sr-only">Toggle theme</span></button></div></div></header><main class="flex-1 overflow-y-auto p-6"><div class="space-y-6"><div><h1 class="text-3xl font-bold tracking-tight">Dashboard</h1><p class="text-muted-foreground">Welcome to QRSAMS - QR-Code Based Student Attendance and Monitoring System</p></div><div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4"><div data-slot="card" class="bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm"><div data-slot="card-header" class="@container/card-header auto-rows-min grid-rows-[auto_auto] gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 flex flex-row items-center justify-between space-y-0 pb-2"><div data-slot="card-title" class="text-sm font-medium">Total Students</div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-users h-4 w-4 text-muted-foreground" aria-hidden="true"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><path d="M16 3.128a4 4 0 0 1 0 7.744"></path><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><circle cx="9" cy="7" r="4"></circle></svg></div><div data-slot="card-content" class="px-6"><div class="text-2xl font-bold">1,234</div><p class="text-xs text-muted-foreground">+20 from last month</p></div></div><div data-slot="card" class="bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm"><div data-slot="card-header" class="@container/card-header auto-rows-min grid-rows-[auto_auto] gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 flex flex-row items-center justify-between space-y-0 pb-2"><div data-slot="card-title" class="text-sm font-medium">Today&#x27;s Attendance</div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clipboard-check h-4 w-4 text-muted-foreground" aria-hidden="true"><rect width="8" height="4" x="8" y="2" rx="1" ry="1"></rect><path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path><path d="m9 14 2 2 4-4"></path></svg></div><div data-slot="card-content" class="px-6"><div class="text-2xl font-bold">89.5%</div><p class="text-xs text-muted-foreground">+2.1% from yesterday</p></div></div><div data-slot="card" class="bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm"><div data-slot="card-header" class="@container/card-header auto-rows-min grid-rows-[auto_auto] gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 flex flex-row items-center justify-between space-y-0 pb-2"><div data-slot="card-title" class="text-sm font-medium">Weekly Average</div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trending-up h-4 w-4 text-muted-foreground" aria-hidden="true"><path d="M16 7h6v6"></path><path d="m22 7-8.5 8.5-5-5L2 17"></path></svg></div><div data-slot="card-content" class="px-6"><div class="text-2xl font-bold">92.3%</div><p class="text-xs text-muted-foreground">+5.2% from last week</p></div></div><div data-slot="card" class="bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm"><div data-slot="card-header" class="@container/card-header auto-rows-min grid-rows-[auto_auto] gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 flex flex-row items-center justify-between space-y-0 pb-2"><div data-slot="card-title" class="text-sm font-medium">Active Classes</div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar h-4 w-4 text-muted-foreground" aria-hidden="true"><path d="M8 2v4"></path><path d="M16 2v4"></path><rect width="18" height="18" x="3" y="4" rx="2"></rect><path d="M3 10h18"></path></svg></div><div data-slot="card-content" class="px-6"><div class="text-2xl font-bold">24</div><p class="text-xs text-muted-foreground">8 ongoing sessions</p></div></div></div><div class="grid gap-4 md:grid-cols-2"><div data-slot="card" class="bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm"><div data-slot="card-header" class="@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6"><div data-slot="card-title" class="leading-none font-semibold">Recent Attendance</div><div data-slot="card-description" class="text-muted-foreground text-sm">Latest student check-ins</div></div><div data-slot="card-content" class="px-6"><div class="space-y-4"><div class="flex items-center justify-between"><div><p class="text-sm font-medium">John Doe</p><p class="text-xs text-muted-foreground">8:30 AM</p></div><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-primary text-primary-foreground [a&amp;]:hover:bg-primary/90">present</span></div><div class="flex items-center justify-between"><div><p class="text-sm font-medium">Jane Smith</p><p class="text-xs text-muted-foreground">8:25 AM</p></div><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-primary text-primary-foreground [a&amp;]:hover:bg-primary/90">present</span></div><div class="flex items-center justify-between"><div><p class="text-sm font-medium">Mike Johnson</p><p class="text-xs text-muted-foreground">8:45 AM</p></div><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&amp;]:hover:bg-secondary/90">late</span></div><div class="flex items-center justify-between"><div><p class="text-sm font-medium">Sarah Wilson</p><p class="text-xs text-muted-foreground">8:20 AM</p></div><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-primary text-primary-foreground [a&amp;]:hover:bg-primary/90">present</span></div></div></div></div><div data-slot="card" class="bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm"><div data-slot="card-header" class="@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6"><div data-slot="card-title" class="leading-none font-semibold">System Status</div><div data-slot="card-description" class="text-muted-foreground text-sm">Current system information</div></div><div data-slot="card-content" class="px-6"><div class="space-y-4"><div class="flex items-center justify-between"><span class="text-sm">QR Scanner Status</span><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-primary text-primary-foreground [a&amp;]:hover:bg-primary/90">Online</span></div><div class="flex items-center justify-between"><span class="text-sm">Database Connection</span><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-primary text-primary-foreground [a&amp;]:hover:bg-primary/90">Connected</span></div><div class="flex items-center justify-between"><span class="text-sm">Last Backup</span><span class="text-sm text-muted-foreground">2 hours ago</span></div><div class="flex items-center justify-between"><span class="text-sm">Active Users</span><span class="text-sm text-muted-foreground">12</span></div></div></div></div></div></div><!--$--><!--/$--></main></div></div><script src="/_next/static/chunks/webpack-3c65a00fa6593823.js" id="_R_" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[1321,[\"493\",\"static/chunks/493-9183a606786a8e5a.js\",\"177\",\"static/chunks/app/layout-b91f0cb18d2a387d.js\"],\"ThemeProvider\"]\n3:I[7236,[\"493\",\"static/chunks/493-9183a606786a8e5a.js\",\"177\",\"static/chunks/app/layout-b91f0cb18d2a387d.js\"],\"AuthProvider\"]\n4:I[7555,[],\"\"]\n5:I[1295,[],\"\"]\n6:I[4364,[\"277\",\"static/chunks/277-6cd9c283f70fe2a1.js\",\"493\",\"static/chunks/493-9183a606786a8e5a.js\",\"491\",\"static/chunks/491-945cd5538409c224.js\",\"357\",\"static/chunks/357-d104fcae1d10ae74.js\",\"305\",\"static/chunks/app/(dashboard)/layout-71e46c4c83e575b3.js\"],\"AppSidebar\"]\n7:I[9633,[\"277\",\"static/chunks/277-6cd9c283f70fe2a1.js\",\"493\",\"static/chunks/493-9183a606786a8e5a.js\",\"491\",\"static/chunks/491-945cd5538409c224.js\",\"357\",\"static/chunks/357-d104fcae1d10ae74.js\",\"305\",\"static/chunks/app/(dashboard)/layout-71e46c4c83e575b3.js\"],\"Header\"]\n13:I[8393,[],\"\"]\n:HL[\"/_next/static/css/9607e0db544c62ae.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"uMSIbYafsYeq_W1FcyE44\",\"p\":\"\",\"c\":[\"\",\"dashboard\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"(dashboard)\",{\"children\":[\"dashboard\",{\"children\":[\"__PAGE__\",{}]}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/9607e0db544c62ae.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"suppressHydrationWarning\":true,\"children\":[\"$\",\"body\",null,{\"className\":\"__variable_5cfdac __variable_9a8899 antialiased\",\"children\":[\"$\",\"$L2\",null,{\"attribute\":\"class\",\"defaultTheme\":\"system\",\"enableSystem\":true,\"disableTransitionOnChange\":true,\"children\":[\"$\",\"$L3\",null,{\"children\":[\"$\",\"$L4\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L5\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]}]}]]}],{\"children\":[\"(dashboard)\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"div\",null,{\"className\":\"flex h-screen bg-background\",\"children\":[[\"$\",\"aside\",null,{\"className\":\"hidden md:block\",\"children\":[\"$\",\"$L6\",null,{}]}],[\"$\",\"div\",null,{\"className\":\"flex-1 flex flex-col overflow-hidden\",\"children\":[[\"$\",\"$L7\",null,{}],[\"$\",\"main\",null,{\"className\":\"flex-1 overflow-y-auto p-6\",\"children\":[\"$\",\"$L4\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L5\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":\"$0:f:0:1:1:props:children:1:props:children:props:children:props:children:props:children:props:notFound:0:1:props:style\",\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":\"$0:f:0:1:1:props:children:1:props:children:props:children:props:children:props:children:props:notFound:0:1:props:children:props:children:1:props:style\",\"children\":404}],[\"$\",\"div\",null,{\"style\":\"$0:f:0:1:1:props:children:1:props:children:props:children:props:children:props:children:props:notFound:0:1:props:children:props:children:2:props:style\",\"children\":[\"$\",\"h2\",null,{\"style\":\"$0:f:0:1:1:props:children:1:props:children:props:children:props:children:props:children:props:notFound:0:1:props:children:props:children:2:props:children:props:style\",\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]]}]]}]]}],{\"children\":[\"dashboard\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L4\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L5\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"div\",null,{\"className\":\"space-y-6\",\"children\":[[\"$\",\"div\",null,{\"children\":[[\"$\",\"h1\",null,{\"className\":\"text-3xl font-bold tracking-tight\",\"children\":\"Dashboard\"}],[\"$\",\"p\",null,{\"className\":\"text-muted-foreground\",\"children\":\"Welcome to QRSAMS - QR-Code Based Student Attendance and Monitoring System\"}]]}],[\"$\",\"div\",null,{\"className\":\"grid gap-4 md:grid-cols-2 lg:grid-cols-4\",\"children\":[[\"$\",\"div\",null,{\"data-slot\":\"card\",\"className\":\"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\"children\":[[\"$\",\"div\",null,{\"data-slot\":\"card-header\",\"className\":\"@container/card-header auto-rows-min grid-rows-[auto_auto] gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 flex flex-row items-center justify-between space-y-0 pb-2\",\"children\":[[\"$\",\"div\",null,{\"data-slot\":\"card-title\",\"className\":\"text-sm font-medium\",\"children\":\"Total Students\"}],[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-users h-4 w-4 text-muted-foreground\",\"aria-hidden\":\"true\",\"children\":[\"$L8\",\"$L9\",\"$La\",\"$Lb\",\"$undefined\"]}]]}],\"$Lc\"]}],\"$Ld\",\"$Le\",\"$Lf\"]}],\"$L10\"]}],null,\"$L11\"]}],{},null,false]},null,false]},null,false]},null,false],\"$L12\",false]],\"m\":\"$undefined\",\"G\":[\"$13\",[]],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"15:I[9665,[],\"OutletBoundary\"]\n17:I[4911,[],\"AsyncMetadataOutlet\"]\n19:I[9665,[],\"ViewportBoundary\"]\n1b:I[9665,[],\"MetadataBoundary\"]\n1c:\"$Sreact.suspense\"\n8:[\"$\",\"path\",\"1yyitq\",{\"d\":\"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\"}]\n9:[\"$\",\"path\",\"16gr8j\",{\"d\":\"M16 3.128a4 4 0 0 1 0 7.744\"}]\na:[\"$\",\"path\",\"kshegd\",{\"d\":\"M22 21v-2a4 4 0 0 0-3-3.87\"}]\nb:[\"$\",\"circle\",\"nufk8\",{\"cx\":\"9\",\"cy\":\"7\",\"r\":\"4\"}]\nc:[\"$\",\"div\",null,{\"data-slot\":\"card-content\",\"className\":\"px-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"text-2xl font-bold\",\"children\":\"1,234\"}],[\"$\",\"p\",null,{\"className\":\"text-xs text-muted-foreground\",\"children\":\"+20 from last month\"}]]}]\n"])</script><script>self.__next_f.push([1,"d:[\"$\",\"div\",null,{\"data-slot\":\"card\",\"className\":\"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\"children\":[[\"$\",\"div\",null,{\"data-slot\":\"card-header\",\"className\":\"@container/card-header auto-rows-min grid-rows-[auto_auto] gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 flex flex-row items-center justify-between space-y-0 pb-2\",\"children\":[[\"$\",\"div\",null,{\"data-slot\":\"card-title\",\"className\":\"text-sm font-medium\",\"children\":\"Today's Attendance\"}],[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-clipboard-check h-4 w-4 text-muted-foreground\",\"aria-hidden\":\"true\",\"children\":[[\"$\",\"rect\",\"tgr4d6\",{\"width\":\"8\",\"height\":\"4\",\"x\":\"8\",\"y\":\"2\",\"rx\":\"1\",\"ry\":\"1\"}],[\"$\",\"path\",\"116196\",{\"d\":\"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2\"}],[\"$\",\"path\",\"df797q\",{\"d\":\"m9 14 2 2 4-4\"}],\"$undefined\"]}]]}],[\"$\",\"div\",null,{\"data-slot\":\"card-content\",\"className\":\"px-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"text-2xl font-bold\",\"children\":\"89.5%\"}],[\"$\",\"p\",null,{\"className\":\"text-xs text-muted-foreground\",\"children\":\"+2.1% from yesterday\"}]]}]]}]\n"])</script><script>self.__next_f.push([1,"e:[\"$\",\"div\",null,{\"data-slot\":\"card\",\"className\":\"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\"children\":[[\"$\",\"div\",null,{\"data-slot\":\"card-header\",\"className\":\"@container/card-header auto-rows-min grid-rows-[auto_auto] gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 flex flex-row items-center justify-between space-y-0 pb-2\",\"children\":[[\"$\",\"div\",null,{\"data-slot\":\"card-title\",\"className\":\"text-sm font-medium\",\"children\":\"Weekly Average\"}],[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-trending-up h-4 w-4 text-muted-foreground\",\"aria-hidden\":\"true\",\"children\":[[\"$\",\"path\",\"box55l\",{\"d\":\"M16 7h6v6\"}],[\"$\",\"path\",\"1t1m79\",{\"d\":\"m22 7-8.5 8.5-5-5L2 17\"}],\"$undefined\"]}]]}],[\"$\",\"div\",null,{\"data-slot\":\"card-content\",\"className\":\"px-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"text-2xl font-bold\",\"children\":\"92.3%\"}],[\"$\",\"p\",null,{\"className\":\"text-xs text-muted-foreground\",\"children\":\"+5.2% from last week\"}]]}]]}]\n"])</script><script>self.__next_f.push([1,"f:[\"$\",\"div\",null,{\"data-slot\":\"card\",\"className\":\"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\"children\":[[\"$\",\"div\",null,{\"data-slot\":\"card-header\",\"className\":\"@container/card-header auto-rows-min grid-rows-[auto_auto] gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 flex flex-row items-center justify-between space-y-0 pb-2\",\"children\":[[\"$\",\"div\",null,{\"data-slot\":\"card-title\",\"className\":\"text-sm font-medium\",\"children\":\"Active Classes\"}],[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-calendar h-4 w-4 text-muted-foreground\",\"aria-hidden\":\"true\",\"children\":[[\"$\",\"path\",\"1cmpym\",{\"d\":\"M8 2v4\"}],[\"$\",\"path\",\"4m81vk\",{\"d\":\"M16 2v4\"}],[\"$\",\"rect\",\"1hopcy\",{\"width\":\"18\",\"height\":\"18\",\"x\":\"3\",\"y\":\"4\",\"rx\":\"2\"}],[\"$\",\"path\",\"8toen8\",{\"d\":\"M3 10h18\"}],\"$undefined\"]}]]}],[\"$\",\"div\",null,{\"data-slot\":\"card-content\",\"className\":\"px-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"text-2xl font-bold\",\"children\":\"24\"}],[\"$\",\"p\",null,{\"className\":\"text-xs text-muted-foreground\",\"children\":\"8 ongoing sessions\"}]]}]]}]\n"])</script><script>self.__next_f.push([1,"10:[\"$\",\"div\",null,{\"className\":\"grid gap-4 md:grid-cols-2\",\"children\":[[\"$\",\"div\",null,{\"data-slot\":\"card\",\"className\":\"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\"children\":[[\"$\",\"div\",null,{\"data-slot\":\"card-header\",\"className\":\"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\"children\":[[\"$\",\"div\",null,{\"data-slot\":\"card-title\",\"className\":\"leading-none font-semibold\",\"children\":\"Recent Attendance\"}],[\"$\",\"div\",null,{\"data-slot\":\"card-description\",\"className\":\"text-muted-foreground text-sm\",\"children\":\"Latest student check-ins\"}]]}],[\"$\",\"div\",null,{\"data-slot\":\"card-content\",\"className\":\"px-6\",\"children\":[\"$\",\"div\",null,{\"className\":\"space-y-4\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"flex items-center justify-between\",\"children\":[[\"$\",\"div\",null,{\"children\":[[\"$\",\"p\",null,{\"className\":\"text-sm font-medium\",\"children\":\"John Doe\"}],[\"$\",\"p\",null,{\"className\":\"text-xs text-muted-foreground\",\"children\":\"8:30 AM\"}]]}],[\"$\",\"span\",null,{\"data-slot\":\"badge\",\"className\":\"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [\u0026\u003esvg]:size-3 gap-1 [\u0026\u003esvg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-primary text-primary-foreground [a\u0026]:hover:bg-primary/90\",\"children\":\"present\"}]]}],[\"$\",\"div\",\"1\",{\"className\":\"flex items-center justify-between\",\"children\":[[\"$\",\"div\",null,{\"children\":[[\"$\",\"p\",null,{\"className\":\"text-sm font-medium\",\"children\":\"Jane Smith\"}],[\"$\",\"p\",null,{\"className\":\"text-xs text-muted-foreground\",\"children\":\"8:25 AM\"}]]}],[\"$\",\"span\",null,{\"data-slot\":\"badge\",\"className\":\"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [\u0026\u003esvg]:size-3 gap-1 [\u0026\u003esvg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-primary text-primary-foreground [a\u0026]:hover:bg-primary/90\",\"children\":\"present\"}]]}],[\"$\",\"div\",\"2\",{\"className\":\"flex items-center justify-between\",\"children\":[[\"$\",\"div\",null,{\"children\":[[\"$\",\"p\",null,{\"className\":\"text-sm font-medium\",\"children\":\"Mike Johnson\"}],[\"$\",\"p\",null,{\"className\":\"text-xs text-muted-foreground\",\"children\":\"8:45 AM\"}]]}],[\"$\",\"span\",null,{\"data-slot\":\"badge\",\"className\":\"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [\u0026\u003esvg]:size-3 gap-1 [\u0026\u003esvg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a\u0026]:hover:bg-secondary/90\",\"children\":\"late\"}]]}],[\"$\",\"div\",\"3\",{\"className\":\"flex items-center justify-between\",\"children\":[[\"$\",\"div\",null,{\"children\":[[\"$\",\"p\",null,{\"className\":\"text-sm font-medium\",\"children\":\"Sarah Wilson\"}],[\"$\",\"p\",null,{\"className\":\"text-xs text-muted-foreground\",\"children\":\"8:20 AM\"}]]}],[\"$\",\"span\",null,{\"data-slot\":\"badge\",\"className\":\"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [\u0026\u003esvg]:size-3 gap-1 [\u0026\u003esvg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-primary text-primary-foreground [a\u0026]:hover:bg-primary/90\",\"children\":\"present\"}]]}]]}]}]]}],\"$L14\"]}]\n"])</script><script>self.__next_f.push([1,"11:[\"$\",\"$L15\",null,{\"children\":[\"$L16\",[\"$\",\"$L17\",null,{\"promise\":\"$@18\"}]]}]\n12:[\"$\",\"$1\",\"h\",{\"children\":[null,[[\"$\",\"$L19\",null,{\"children\":\"$L1a\"}],null],[\"$\",\"$L1b\",null,{\"children\":[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$1c\",null,{\"fallback\":null,\"children\":\"$L1d\"}]}]}]]}]\n"])</script><script>self.__next_f.push([1,"14:[\"$\",\"div\",null,{\"data-slot\":\"card\",\"className\":\"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\"children\":[[\"$\",\"div\",null,{\"data-slot\":\"card-header\",\"className\":\"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\"children\":[[\"$\",\"div\",null,{\"data-slot\":\"card-title\",\"className\":\"leading-none font-semibold\",\"children\":\"System Status\"}],[\"$\",\"div\",null,{\"data-slot\":\"card-description\",\"className\":\"text-muted-foreground text-sm\",\"children\":\"Current system information\"}]]}],[\"$\",\"div\",null,{\"data-slot\":\"card-content\",\"className\":\"px-6\",\"children\":[\"$\",\"div\",null,{\"className\":\"space-y-4\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex items-center justify-between\",\"children\":[[\"$\",\"span\",null,{\"className\":\"text-sm\",\"children\":\"QR Scanner Status\"}],[\"$\",\"span\",null,{\"data-slot\":\"badge\",\"className\":\"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [\u0026\u003esvg]:size-3 gap-1 [\u0026\u003esvg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-primary text-primary-foreground [a\u0026]:hover:bg-primary/90\",\"children\":\"Online\"}]]}],[\"$\",\"div\",null,{\"className\":\"flex items-center justify-between\",\"children\":[[\"$\",\"span\",null,{\"className\":\"text-sm\",\"children\":\"Database Connection\"}],[\"$\",\"span\",null,{\"data-slot\":\"badge\",\"className\":\"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [\u0026\u003esvg]:size-3 gap-1 [\u0026\u003esvg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-primary text-primary-foreground [a\u0026]:hover:bg-primary/90\",\"children\":\"Connected\"}]]}],[\"$\",\"div\",null,{\"className\":\"flex items-center justify-between\",\"children\":[[\"$\",\"span\",null,{\"className\":\"text-sm\",\"children\":\"Last Backup\"}],[\"$\",\"span\",null,{\"className\":\"text-sm text-muted-foreground\",\"children\":\"2 hours ago\"}]]}],[\"$\",\"div\",null,{\"className\":\"flex items-center justify-between\",\"children\":[[\"$\",\"span\",null,{\"className\":\"text-sm\",\"children\":\"Active Users\"}],[\"$\",\"span\",null,{\"className\":\"text-sm text-muted-foreground\",\"children\":\"12\"}]]}]]}]}]]}]\n"])</script><script>self.__next_f.push([1,"1a:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\n16:null\n"])</script><script>self.__next_f.push([1,"1e:I[8175,[],\"IconMark\"]\n18:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"QRSAMS - QR-Code Based Student Attendance and Monitoring System\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Tanauan School of Arts and Trade - Student Attendance Management System\"}],[\"$\",\"link\",\"2\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}],[\"$\",\"$L1e\",\"3\",{}]],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"1d:\"$18:metadata\"\n"])</script></body></html>