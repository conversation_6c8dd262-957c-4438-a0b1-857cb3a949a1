{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/providers/theme-provider.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { ThemeProvider as NextThemesProvider } from \"next-themes\"\n\ninterface ThemeProviderProps {\n  children: React.ReactNode\n  attribute?: \"class\" | \"data-theme\" | \"data-mode\"\n  defaultTheme?: string\n  enableSystem?: boolean\n  disableTransitionOnChange?: boolean\n}\n\nexport function ThemeProvider({ children, ...props }: ThemeProviderProps) {\n  return <NextThemesProvider {...props}>{children}</NextThemesProvider>\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAaO,SAAS,cAAc,KAA0C;QAA1C,EAAE,QAAQ,EAAE,GAAG,OAA2B,GAA1C;IAC5B,qBAAO,6LAAC,mJAAA,CAAA,gBAAkB;QAAE,GAAG,KAAK;kBAAG;;;;;;AACzC;KAFgB", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/providers/session-provider.tsx"], "sourcesContent": ["\"use client\"\n\nimport { SessionProvider } from \"next-auth/react\"\nimport { ReactNode } from \"react\"\n\ninterface AuthProviderProps {\n  children: ReactNode\n}\n\nexport function AuthProvider({ children }: AuthProviderProps) {\n  return <SessionProvider>{children}</SessionProvider>\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASO,SAAS,aAAa,KAA+B;QAA/B,EAAE,QAAQ,EAAqB,GAA/B;IAC3B,qBAAO,6LAAC,wIAAA,CAAA,kBAAe;kBAAE;;;;;;AAC3B;KAFgB", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return type.displayName || \"Context\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n      REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,KAAK,WAAW,IAAI;YAC7B,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,0BAA0B,SAAU,iBAAiB;YACnD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,MAAM,wBAAwB,CAAC,IAAI,CAC9D,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 275, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA;;KAEO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 286, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/next-themes/dist/index.mjs"], "sourcesContent": ["\"use client\";import*as t from\"react\";var M=(e,i,s,u,m,a,l,h)=>{let d=document.documentElement,w=[\"light\",\"dark\"];function p(n){(Array.isArray(e)?e:[e]).forEach(y=>{let k=y===\"class\",S=k&&a?m.map(f=>a[f]||f):m;k?(d.classList.remove(...S),d.classList.add(a&&a[n]?a[n]:n)):d.setAttribute(y,n)}),R(n)}function R(n){h&&w.includes(n)&&(d.style.colorScheme=n)}function c(){return window.matchMedia(\"(prefers-color-scheme: dark)\").matches?\"dark\":\"light\"}if(u)p(u);else try{let n=localStorage.getItem(i)||s,y=l&&n===\"system\"?c():n;p(y)}catch(n){}};var b=[\"light\",\"dark\"],I=\"(prefers-color-scheme: dark)\",O=typeof window==\"undefined\",x=t.createContext(void 0),U={setTheme:e=>{},themes:[]},z=()=>{var e;return(e=t.useContext(x))!=null?e:U},J=e=>t.useContext(x)?t.createElement(t.Fragment,null,e.children):t.createElement(V,{...e}),N=[\"light\",\"dark\"],V=({forcedTheme:e,disableTransitionOnChange:i=!1,enableSystem:s=!0,enableColorScheme:u=!0,storageKey:m=\"theme\",themes:a=N,defaultTheme:l=s?\"system\":\"light\",attribute:h=\"data-theme\",value:d,children:w,nonce:p,scriptProps:R})=>{let[c,n]=t.useState(()=>H(m,l)),[T,y]=t.useState(()=>c===\"system\"?E():c),k=d?Object.values(d):a,S=t.useCallback(o=>{let r=o;if(!r)return;o===\"system\"&&s&&(r=E());let v=d?d[r]:r,C=i?W(p):null,P=document.documentElement,L=g=>{g===\"class\"?(P.classList.remove(...k),v&&P.classList.add(v)):g.startsWith(\"data-\")&&(v?P.setAttribute(g,v):P.removeAttribute(g))};if(Array.isArray(h)?h.forEach(L):L(h),u){let g=b.includes(l)?l:null,D=b.includes(r)?r:g;P.style.colorScheme=D}C==null||C()},[p]),f=t.useCallback(o=>{let r=typeof o==\"function\"?o(c):o;n(r);try{localStorage.setItem(m,r)}catch(v){}},[c]),A=t.useCallback(o=>{let r=E(o);y(r),c===\"system\"&&s&&!e&&S(\"system\")},[c,e]);t.useEffect(()=>{let o=window.matchMedia(I);return o.addListener(A),A(o),()=>o.removeListener(A)},[A]),t.useEffect(()=>{let o=r=>{r.key===m&&(r.newValue?n(r.newValue):f(l))};return window.addEventListener(\"storage\",o),()=>window.removeEventListener(\"storage\",o)},[f]),t.useEffect(()=>{S(e!=null?e:c)},[e,c]);let Q=t.useMemo(()=>({theme:c,setTheme:f,forcedTheme:e,resolvedTheme:c===\"system\"?T:c,themes:s?[...a,\"system\"]:a,systemTheme:s?T:void 0}),[c,f,e,T,s,a]);return t.createElement(x.Provider,{value:Q},t.createElement(_,{forcedTheme:e,storageKey:m,attribute:h,enableSystem:s,enableColorScheme:u,defaultTheme:l,value:d,themes:a,nonce:p,scriptProps:R}),w)},_=t.memo(({forcedTheme:e,storageKey:i,attribute:s,enableSystem:u,enableColorScheme:m,defaultTheme:a,value:l,themes:h,nonce:d,scriptProps:w})=>{let p=JSON.stringify([s,i,a,e,h,l,u,m]).slice(1,-1);return t.createElement(\"script\",{...w,suppressHydrationWarning:!0,nonce:typeof window==\"undefined\"?d:\"\",dangerouslySetInnerHTML:{__html:`(${M.toString()})(${p})`}})}),H=(e,i)=>{if(O)return;let s;try{s=localStorage.getItem(e)||void 0}catch(u){}return s||i},W=e=>{let i=document.createElement(\"style\");return e&&i.setAttribute(\"nonce\",e),i.appendChild(document.createTextNode(\"*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}\")),document.head.appendChild(i),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(i)},1)}},E=e=>(e||(e=window.matchMedia(I)),e.matches?\"dark\":\"light\");export{J as ThemeProvider,z as useTheme};\n"], "names": [], "mappings": ";;;;AAAa;AAAb;;AAAqC,IAAI,IAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;IAAK,IAAI,IAAE,SAAS,eAAe,EAAC,IAAE;QAAC;QAAQ;KAAO;IAAC,SAAS,EAAE,CAAC;QAAE,CAAC,MAAM,OAAO,CAAC,KAAG,IAAE;YAAC;SAAE,EAAE,OAAO,CAAC,CAAA;YAAI,IAAI,IAAE,MAAI,SAAQ,IAAE,KAAG,IAAE,EAAE,GAAG,CAAC,CAAA,IAAG,CAAC,CAAC,EAAE,IAAE,KAAG;YAAE,IAAE,CAAC,EAAE,SAAS,CAAC,MAAM,IAAI,IAAG,EAAE,SAAS,CAAC,GAAG,CAAC,KAAG,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE,GAAC,EAAE,IAAE,EAAE,YAAY,CAAC,GAAE;QAAE,IAAG,EAAE;IAAE;IAAC,SAAS,EAAE,CAAC;QAAE,KAAG,EAAE,QAAQ,CAAC,MAAI,CAAC,EAAE,KAAK,CAAC,WAAW,GAAC,CAAC;IAAC;IAAC,SAAS;QAAI,OAAO,OAAO,UAAU,CAAC,gCAAgC,OAAO,GAAC,SAAO;IAAO;IAAC,IAAG,GAAE,EAAE;SAAQ,IAAG;QAAC,IAAI,IAAE,aAAa,OAAO,CAAC,MAAI,GAAE,IAAE,KAAG,MAAI,WAAS,MAAI;QAAE,EAAE;IAAE,EAAC,OAAM,GAAE,CAAC;AAAC;AAAE,IAAI,IAAE;IAAC;IAAQ;CAAO,EAAC,IAAE,gCAA+B,IAAE,OAAO,UAAQ,aAAY,IAAE,6JAAA,CAAA,gBAAe,CAAC,KAAK,IAAG,IAAE;IAAC,UAAS,CAAA,KAAI;IAAE,QAAO,EAAE;AAAA,GAAE,IAAE;IAAK,IAAI;IAAE,OAAM,CAAC,IAAE,6JAAA,CAAA,aAAY,CAAC,EAAE,KAAG,OAAK,IAAE;AAAC,GAAE,IAAE,CAAA,IAAG,6JAAA,CAAA,aAAY,CAAC,KAAG,6JAAA,CAAA,gBAAe,CAAC,6JAAA,CAAA,WAAU,EAAC,MAAK,EAAE,QAAQ,IAAE,6JAAA,CAAA,gBAAe,CAAC,GAAE;QAAC,GAAG,CAAC;IAAA,IAAG,IAAE;IAAC;IAAQ;CAAO,EAAC,IAAE;QAAC,EAAC,aAAY,CAAC,EAAC,2BAA0B,IAAE,CAAC,CAAC,EAAC,cAAa,IAAE,CAAC,CAAC,EAAC,mBAAkB,IAAE,CAAC,CAAC,EAAC,YAAW,IAAE,OAAO,EAAC,QAAO,IAAE,CAAC,EAAC,cAAa,IAAE,IAAE,WAAS,OAAO,EAAC,WAAU,IAAE,YAAY,EAAC,OAAM,CAAC,EAAC,UAAS,CAAC,EAAC,OAAM,CAAC,EAAC,aAAY,CAAC,EAAC;IAAI,IAAG,CAAC,GAAE,EAAE,GAAC,6JAAA,CAAA,WAAU;sBAAC,IAAI,EAAE,GAAE;sBAAI,CAAC,GAAE,EAAE,GAAC,6JAAA,CAAA,WAAU;sBAAC,IAAI,MAAI,WAAS,MAAI;sBAAG,IAAE,IAAE,OAAO,MAAM,CAAC,KAAG,GAAE,IAAE,6JAAA,CAAA,cAAa;4BAAC,CAAA;YAAI,IAAI,IAAE;YAAE,IAAG,CAAC,GAAE;YAAO,MAAI,YAAU,KAAG,CAAC,IAAE,GAAG;YAAE,IAAI,IAAE,IAAE,CAAC,CAAC,EAAE,GAAC,GAAE,IAAE,IAAE,EAAE,KAAG,MAAK,IAAE,SAAS,eAAe,EAAC;sCAAE,CAAA;oBAAI,MAAI,UAAQ,CAAC,EAAE,SAAS,CAAC,MAAM,IAAI,IAAG,KAAG,EAAE,SAAS,CAAC,GAAG,CAAC,EAAE,IAAE,EAAE,UAAU,CAAC,YAAU,CAAC,IAAE,EAAE,YAAY,CAAC,GAAE,KAAG,EAAE,eAAe,CAAC,EAAE;gBAAC;;YAAE,IAAG,MAAM,OAAO,CAAC,KAAG,EAAE,OAAO,CAAC,KAAG,EAAE,IAAG,GAAE;gBAAC,IAAI,IAAE,EAAE,QAAQ,CAAC,KAAG,IAAE,MAAK,IAAE,EAAE,QAAQ,CAAC,KAAG,IAAE;gBAAE,EAAE,KAAK,CAAC,WAAW,GAAC;YAAC;YAAC,KAAG,QAAM;QAAG;2BAAE;QAAC;KAAE,GAAE,IAAE,6JAAA,CAAA,cAAa;4BAAC,CAAA;YAAI,IAAI,IAAE,OAAO,KAAG,aAAW,EAAE,KAAG;YAAE,EAAE;YAAG,IAAG;gBAAC,aAAa,OAAO,CAAC,GAAE;YAAE,EAAC,OAAM,GAAE,CAAC;QAAC;2BAAE;QAAC;KAAE,GAAE,IAAE,6JAAA,CAAA,cAAa;4BAAC,CAAA;YAAI,IAAI,IAAE,EAAE;YAAG,EAAE,IAAG,MAAI,YAAU,KAAG,CAAC,KAAG,EAAE;QAAS;2BAAE;QAAC;QAAE;KAAE;IAAE,6JAAA,CAAA,YAAW;uBAAC;YAAK,IAAI,IAAE,OAAO,UAAU,CAAC;YAAG,OAAO,EAAE,WAAW,CAAC,IAAG,EAAE;+BAAG,IAAI,EAAE,cAAc,CAAC;;QAAE;sBAAE;QAAC;KAAE,GAAE,6JAAA,CAAA,YAAW;uBAAC;YAAK,IAAI;iCAAE,CAAA;oBAAI,EAAE,GAAG,KAAG,KAAG,CAAC,EAAE,QAAQ,GAAC,EAAE,EAAE,QAAQ,IAAE,EAAE,EAAE;gBAAC;;YAAE,OAAO,OAAO,gBAAgB,CAAC,WAAU;+BAAG,IAAI,OAAO,mBAAmB,CAAC,WAAU;;QAAE;sBAAE;QAAC;KAAE,GAAE,6JAAA,CAAA,YAAW;uBAAC;YAAK,EAAE,KAAG,OAAK,IAAE;QAAE;sBAAE;QAAC;QAAE;KAAE;IAAE,IAAI,IAAE,6JAAA,CAAA,UAAS;wBAAC,IAAI,CAAC;gBAAC,OAAM;gBAAE,UAAS;gBAAE,aAAY;gBAAE,eAAc,MAAI,WAAS,IAAE;gBAAE,QAAO,IAAE;uBAAI;oBAAE;iBAAS,GAAC;gBAAE,aAAY,IAAE,IAAE,KAAK;YAAC,CAAC;uBAAE;QAAC;QAAE;QAAE;QAAE;QAAE;QAAE;KAAE;IAAE,OAAO,6JAAA,CAAA,gBAAe,CAAC,EAAE,QAAQ,EAAC;QAAC,OAAM;IAAC,GAAE,6JAAA,CAAA,gBAAe,CAAC,GAAE;QAAC,aAAY;QAAE,YAAW;QAAE,WAAU;QAAE,cAAa;QAAE,mBAAkB;QAAE,cAAa;QAAE,OAAM;QAAE,QAAO;QAAE,OAAM;QAAE,aAAY;IAAC,IAAG;AAAE,GAAE,IAAE,6JAAA,CAAA,OAAM,CAAC;QAAC,EAAC,aAAY,CAAC,EAAC,YAAW,CAAC,EAAC,WAAU,CAAC,EAAC,cAAa,CAAC,EAAC,mBAAkB,CAAC,EAAC,cAAa,CAAC,EAAC,OAAM,CAAC,EAAC,QAAO,CAAC,EAAC,OAAM,CAAC,EAAC,aAAY,CAAC,EAAC;IAAI,IAAI,IAAE,KAAK,SAAS,CAAC;QAAC;QAAE;QAAE;QAAE;QAAE;QAAE;QAAE;QAAE;KAAE,EAAE,KAAK,CAAC,GAAE,CAAC;IAAG,OAAO,6JAAA,CAAA,gBAAe,CAAC,UAAS;QAAC,GAAG,CAAC;QAAC,0BAAyB,CAAC;QAAE,OAAM,OAAO,UAAQ,cAAY,IAAE;QAAG,yBAAwB;YAAC,QAAO,AAAC,IAAoB,OAAjB,EAAE,QAAQ,IAAG,MAAM,OAAF,GAAE;QAAE;IAAC;AAAE,IAAG,IAAE,CAAC,GAAE;IAAK,IAAG,GAAE;IAAO,IAAI;IAAE,IAAG;QAAC,IAAE,aAAa,OAAO,CAAC,MAAI,KAAK;IAAC,EAAC,OAAM,GAAE,CAAC;IAAC,OAAO,KAAG;AAAC,GAAE,IAAE,CAAA;IAAI,IAAI,IAAE,SAAS,aAAa,CAAC;IAAS,OAAO,KAAG,EAAE,YAAY,CAAC,SAAQ,IAAG,EAAE,WAAW,CAAC,SAAS,cAAc,CAAC,iLAAgL,SAAS,IAAI,CAAC,WAAW,CAAC,IAAG;QAAK,OAAO,gBAAgB,CAAC,SAAS,IAAI,GAAE,WAAW;YAAK,SAAS,IAAI,CAAC,WAAW,CAAC;QAAE,GAAE;IAAE;AAAC,GAAE,IAAE,CAAA,IAAG,CAAC,KAAG,CAAC,IAAE,OAAO,UAAU,CAAC,EAAE,GAAE,EAAE,OAAO,GAAC,SAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 479, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40auth/core/errors.js"], "sourcesContent": ["/**\n * Base error class for all Auth.js errors.\n * It's optimized to be printed in the server logs in a nicely formatted way\n * via the [`logger.error`](https://authjs.dev/reference/core#logger) option.\n * @noInheritDoc\n */\nexport class AuthError extends Error {\n    /** @internal */\n    constructor(message, errorOptions) {\n        if (message instanceof Error) {\n            super(undefined, {\n                cause: { err: message, ...message.cause, ...errorOptions },\n            });\n        }\n        else if (typeof message === \"string\") {\n            if (errorOptions instanceof Error) {\n                errorOptions = { err: errorOptions, ...errorOptions.cause };\n            }\n            super(message, errorOptions);\n        }\n        else {\n            super(undefined, message);\n        }\n        this.name = this.constructor.name;\n        // @ts-expect-error https://github.com/microsoft/TypeScript/issues/3841\n        this.type = this.constructor.type ?? \"AuthError\";\n        // @ts-expect-error https://github.com/microsoft/TypeScript/issues/3841\n        this.kind = this.constructor.kind ?? \"error\";\n        Error.captureStackTrace?.(this, this.constructor);\n        const url = `https://errors.authjs.dev#${this.type.toLowerCase()}`;\n        this.message += `${this.message ? \". \" : \"\"}Read more at ${url}`;\n    }\n}\n/**\n * Thrown when the user's sign-in attempt failed.\n * @noInheritDoc\n */\nexport class SignInError extends AuthError {\n}\n/** @internal */\nSignInError.kind = \"signIn\";\n/**\n * One of the database [`Adapter` methods](https://authjs.dev/reference/core/adapters#methods)\n * failed during execution.\n *\n * :::tip\n * If `debug: true` is set, you can check out `[auth][debug]` in the logs to learn more about the failed adapter method execution.\n * @example\n * ```sh\n * [auth][debug]: adapter_getUserByEmail\n * { \"args\": [undefined] }\n * ```\n * :::\n * @noInheritDoc\n */\nexport class AdapterError extends AuthError {\n}\nAdapterError.type = \"AdapterError\";\n/**\n * Thrown when the execution of the [`signIn` callback](https://authjs.dev/reference/core/types#signin) fails\n * or if it returns `false`.\n * @noInheritDoc\n */\nexport class AccessDenied extends AuthError {\n}\nAccessDenied.type = \"AccessDenied\";\n/**\n * This error occurs when the user cannot finish login.\n * Depending on the provider type, this could have happened for multiple reasons.\n *\n * :::tip\n * Check out `[auth][details]` in the logs to know which provider failed.\n * @example\n * ```sh\n * [auth][details]: { \"provider\": \"github\" }\n * ```\n * :::\n *\n * For an [OAuth provider](https://authjs.dev/getting-started/authentication/oauth), possible causes are:\n * - The user denied access to the application\n * - There was an error parsing the OAuth Profile:\n *   Check out the provider's `profile` or `userinfo.request` method to make sure\n *   it correctly fetches the user's profile.\n * - The `signIn` or `jwt` callback methods threw an uncaught error:\n *   Check the callback method implementations.\n *\n * For an [Email provider](https://authjs.dev/getting-started/authentication/email), possible causes are:\n * - The provided email/token combination was invalid/missing:\n *   Check if the provider's `sendVerificationRequest` method correctly sends the email.\n * - The provided email/token combination has expired:\n *   Ask the user to log in again.\n * - There was an error with the database:\n *   Check the database logs.\n *\n * For a [Credentials provider](https://authjs.dev/getting-started/authentication/credentials), possible causes are:\n * - The `authorize` method threw an uncaught error:\n *   Check the provider's `authorize` method.\n * - The `signIn` or `jwt` callback methods threw an uncaught error:\n *   Check the callback method implementations.\n *\n * :::tip\n * Check out `[auth][cause]` in the error message for more details.\n * It will show the original stack trace.\n * :::\n * @noInheritDoc\n */\nexport class CallbackRouteError extends AuthError {\n}\nCallbackRouteError.type = \"CallbackRouteError\";\n/**\n * Thrown when Auth.js is misconfigured and accidentally tried to require authentication on a custom error page.\n * To prevent an infinite loop, Auth.js will instead render its default error page.\n *\n * To fix this, make sure that the `error` page does not require authentication.\n *\n * Learn more at [Guide: Error pages](https://authjs.dev/guides/pages/error)\n * @noInheritDoc\n */\nexport class ErrorPageLoop extends AuthError {\n}\nErrorPageLoop.type = \"ErrorPageLoop\";\n/**\n * One of the [`events` methods](https://authjs.dev/reference/core/types#eventcallbacks)\n * failed during execution.\n *\n * Make sure that the `events` methods are implemented correctly and uncaught errors are handled.\n *\n * Learn more at [`events`](https://authjs.dev/reference/core/types#eventcallbacks)\n * @noInheritDoc\n */\nexport class EventError extends AuthError {\n}\nEventError.type = \"EventError\";\n/**\n * Thrown when Auth.js is unable to verify a `callbackUrl` value.\n * The browser either disabled cookies or the `callbackUrl` is not a valid URL.\n *\n * Somebody might have tried to manipulate the callback URL that Auth.js uses to redirect the user back to the configured `callbackUrl`/page.\n * This could be a malicious hacker trying to redirect the user to a phishing site.\n * To prevent this, Auth.js checks if the callback URL is valid and throws this error if it is not.\n *\n * There is no action required, but it might be an indicator that somebody is trying to attack your application.\n * @noInheritDoc\n */\nexport class InvalidCallbackUrl extends AuthError {\n}\nInvalidCallbackUrl.type = \"InvalidCallbackUrl\";\n/**\n * Can be thrown from the `authorize` callback of the Credentials provider.\n * When an error occurs during the `authorize` callback, two things can happen:\n * 1. The user is redirected to the signin page, with `error=CredentialsSignin&code=credentials` in the URL. `code` is configurable.\n * 2. If you throw this error in a framework that handles form actions server-side, this error is thrown, instead of redirecting the user, so you'll need to handle.\n * @noInheritDoc\n */\nexport class CredentialsSignin extends SignInError {\n    constructor() {\n        super(...arguments);\n        /**\n         * The error code that is set in the `code` query parameter of the redirect URL.\n         *\n         *\n         * ⚠ NOTE: This property is going to be included in the URL, so make sure it does not hint at sensitive errors.\n         *\n         * The full error is always logged on the server, if you need to debug.\n         *\n         * Generally, we don't recommend hinting specifically if the user had either a wrong username or password specifically,\n         * try rather something like \"Invalid credentials\".\n         */\n        this.code = \"credentials\";\n    }\n}\nCredentialsSignin.type = \"CredentialsSignin\";\n/**\n * One of the configured OAuth or OIDC providers is missing the `authorization`, `token` or `userinfo`, or `issuer` configuration.\n * To perform OAuth or OIDC sign in, at least one of these endpoints is required.\n *\n * Learn more at [`OAuth2Config`](https://authjs.dev/reference/core/providers#oauth2configprofile) or [Guide: OAuth Provider](https://authjs.dev/guides/configuring-oauth-providers)\n * @noInheritDoc\n */\nexport class InvalidEndpoints extends AuthError {\n}\nInvalidEndpoints.type = \"InvalidEndpoints\";\n/**\n * Thrown when a PKCE, state or nonce OAuth check could not be performed.\n * This could happen if the OAuth provider is configured incorrectly or if the browser is blocking cookies.\n *\n * Learn more at [`checks`](https://authjs.dev/reference/core/providers#checks)\n * @noInheritDoc\n */\nexport class InvalidCheck extends AuthError {\n}\nInvalidCheck.type = \"InvalidCheck\";\n/**\n * Logged on the server when Auth.js could not decode or encode a JWT-based (`strategy: \"jwt\"`) session.\n *\n * Possible causes are either a misconfigured `secret` or a malformed JWT or `encode/decode` methods.\n *\n * :::note\n * When this error is logged, the session cookie is destroyed.\n * :::\n *\n * Learn more at [`secret`](https://authjs.dev/reference/core#secret), [`jwt.encode`](https://authjs.dev/reference/core/jwt#encode-1) or [`jwt.decode`](https://authjs.dev/reference/core/jwt#decode-2) for more information.\n * @noInheritDoc\n */\nexport class JWTSessionError extends AuthError {\n}\nJWTSessionError.type = \"JWTSessionError\";\n/**\n * Thrown if Auth.js is misconfigured. This could happen if you configured an Email provider but did not set up a database adapter,\n * or tried using a `strategy: \"database\"` session without a database adapter.\n * In both cases, make sure you either remove the configuration or add the missing adapter.\n *\n * Learn more at [Database Adapters](https://authjs.dev/getting-started/database), [Email provider](https://authjs.dev/getting-started/authentication/email) or [Concept: Database session strategy](https://authjs.dev/concepts/session-strategies#database-session)\n * @noInheritDoc\n */\nexport class MissingAdapter extends AuthError {\n}\nMissingAdapter.type = \"MissingAdapter\";\n/**\n * Thrown similarily to [`MissingAdapter`](https://authjs.dev/reference/core/errors#missingadapter), but only some required methods were missing.\n *\n * Make sure you either remove the configuration or add the missing methods to the adapter.\n *\n * Learn more at [Database Adapters](https://authjs.dev/getting-started/database)\n * @noInheritDoc\n */\nexport class MissingAdapterMethods extends AuthError {\n}\nMissingAdapterMethods.type = \"MissingAdapterMethods\";\n/**\n * Thrown when a Credentials provider is missing the `authorize` configuration.\n * To perform credentials sign in, the `authorize` method is required.\n *\n * Learn more at [Credentials provider](https://authjs.dev/getting-started/authentication/credentials)\n * @noInheritDoc\n */\nexport class MissingAuthorize extends AuthError {\n}\nMissingAuthorize.type = \"MissingAuthorize\";\n/**\n * Auth.js requires a secret or multiple secrets to be set, but none was not found. This is used to encrypt cookies, JWTs and other sensitive data.\n *\n * :::note\n * If you are using a framework like Next.js, we try to automatically infer the secret from the `AUTH_SECRET`, `AUTH_SECRET_1`, etc. environment variables.\n * Alternatively, you can also explicitly set the [`AuthConfig.secret`](https://authjs.dev/reference/core#secret) option.\n * :::\n *\n *\n * :::tip\n * To generate a random string, you can use the Auth.js CLI: `npx auth secret`\n * :::\n * @noInheritDoc\n */\nexport class MissingSecret extends AuthError {\n}\nMissingSecret.type = \"MissingSecret\";\n/**\n * Thrown when an Email address is already associated with an account\n * but the user is trying an OAuth account that is not linked to it.\n *\n * For security reasons, Auth.js does not automatically link OAuth accounts to existing accounts if the user is not signed in.\n *\n * :::tip\n * If you trust the OAuth provider to have verified the user's email address,\n * you can enable automatic account linking by setting [`allowDangerousEmailAccountLinking: true`](https://authjs.dev/reference/core/providers#allowdangerousemailaccountlinking)\n * in the provider configuration.\n * :::\n * @noInheritDoc\n */\nexport class OAuthAccountNotLinked extends SignInError {\n}\nOAuthAccountNotLinked.type = \"OAuthAccountNotLinked\";\n/**\n * Thrown when an OAuth provider returns an error during the sign in process.\n * This could happen for example if the user denied access to the application or there was a configuration error.\n *\n * For a full list of possible reasons, check out the specification [Authorization Code Grant: Error Response](https://www.rfc-editor.org/rfc/rfc6749#section-*******)\n * @noInheritDoc\n */\nexport class OAuthCallbackError extends SignInError {\n}\nOAuthCallbackError.type = \"OAuthCallbackError\";\n/**\n * This error occurs during an OAuth sign in attempt when the provider's\n * response could not be parsed. This could for example happen if the provider's API\n * changed, or the [`OAuth2Config.profile`](https://authjs.dev/reference/core/providers#oauth2configprofile) method is not implemented correctly.\n * @noInheritDoc\n */\nexport class OAuthProfileParseError extends AuthError {\n}\nOAuthProfileParseError.type = \"OAuthProfileParseError\";\n/**\n * Logged on the server when Auth.js could not retrieve a session from the database (`strategy: \"database\"`).\n *\n * The database adapter might be misconfigured or the database is not reachable.\n *\n * Learn more at [Concept: Database session strategy](https://authjs.dev/concepts/session-strategies#database)\n * @noInheritDoc\n */\nexport class SessionTokenError extends AuthError {\n}\nSessionTokenError.type = \"SessionTokenError\";\n/**\n * Happens when login by [OAuth](https://authjs.dev/getting-started/authentication/oauth) could not be started.\n *\n * Possible causes are:\n * - The Authorization Server is not compliant with the [OAuth 2.0](https://www.ietf.org/rfc/rfc6749.html) or the [OIDC](https://openid.net/specs/openid-connect-core-1_0.html) specification.\n *   Check the details in the error message.\n *\n * :::tip\n * Check out `[auth][details]` in the logs to know which provider failed.\n * @example\n * ```sh\n * [auth][details]: { \"provider\": \"github\" }\n * ```\n * :::\n * @noInheritDoc\n */\nexport class OAuthSignInError extends SignInError {\n}\nOAuthSignInError.type = \"OAuthSignInError\";\n/**\n * Happens when the login by an [Email provider](https://authjs.dev/getting-started/authentication/email) could not be started.\n *\n * Possible causes are:\n * - The email sent from the client is invalid, could not be normalized by [`EmailConfig.normalizeIdentifier`](https://authjs.dev/reference/core/providers/email#normalizeidentifier)\n * - The provided email/token combination has expired:\n *   Ask the user to log in again.\n * - There was an error with the database:\n *   Check the database logs.\n * @noInheritDoc\n */\nexport class EmailSignInError extends SignInError {\n}\nEmailSignInError.type = \"EmailSignInError\";\n/**\n * Represents an error that occurs during the sign-out process. This error\n * is logged when there are issues in terminating a user's session, either\n * by failing to delete the session from the database (in database session\n * strategies) or encountering issues during other parts of the sign-out\n * process, such as emitting sign-out events or clearing session cookies.\n *\n * The session cookie(s) are emptied even if this error is logged.\n * @noInheritDoc\n */\nexport class SignOutError extends AuthError {\n}\nSignOutError.type = \"SignOutError\";\n/**\n * Auth.js was requested to handle an operation that it does not support.\n *\n * See [`AuthAction`](https://authjs.dev/reference/core/types#authaction) for the supported actions.\n * @noInheritDoc\n */\nexport class UnknownAction extends AuthError {\n}\nUnknownAction.type = \"UnknownAction\";\n/**\n * Thrown when a Credentials provider is present but the JWT strategy (`strategy: \"jwt\"`) is not enabled.\n *\n * Learn more at [`strategy`](https://authjs.dev/reference/core#strategy) or [Credentials provider](https://authjs.dev/getting-started/authentication/credentials)\n * @noInheritDoc\n */\nexport class UnsupportedStrategy extends AuthError {\n}\nUnsupportedStrategy.type = \"UnsupportedStrategy\";\n/**\n * Thrown when an endpoint was incorrectly called without a provider, or with an unsupported provider.\n * @noInheritDoc\n */\nexport class InvalidProvider extends AuthError {\n}\nInvalidProvider.type = \"InvalidProvider\";\n/**\n * Thrown when the `trustHost` option was not set to `true`.\n *\n * Auth.js requires the `trustHost` option to be set to `true` since it's relying on the request headers' `host` value.\n *\n * :::note\n * Official Auth.js libraries might attempt to automatically set the `trustHost` option to `true` if the request is coming from a trusted host on a trusted platform.\n * :::\n *\n * Learn more at [`trustHost`](https://authjs.dev/reference/core#trusthost) or [Guide: Deployment](https://authjs.dev/getting-started/deployment)\n * @noInheritDoc\n */\nexport class UntrustedHost extends AuthError {\n}\nUntrustedHost.type = \"UntrustedHost\";\n/**\n * The user's email/token combination was invalid.\n * This could be because the email/token combination was not found in the database,\n * or because the token has expired. Ask the user to log in again.\n * @noInheritDoc\n */\nexport class Verification extends AuthError {\n}\nVerification.type = \"Verification\";\n/**\n * Error for missing CSRF tokens in client-side actions (`signIn`, `signOut`, `useSession#update`).\n * Thrown when actions lack the double submit cookie, essential for CSRF protection.\n *\n * CSRF ([Cross-Site Request Forgery](https://owasp.org/www-community/attacks/csrf))\n * is an attack leveraging authenticated user credentials for unauthorized actions.\n *\n * Double submit cookie pattern, a CSRF defense, requires matching values in a cookie\n * and request parameter. More on this at [MDN Web Docs](https://developer.mozilla.org/en-US/docs/Glossary/CSRF).\n * @noInheritDoc\n */\nexport class MissingCSRF extends SignInError {\n}\nMissingCSRF.type = \"MissingCSRF\";\nconst clientErrors = new Set([\n    \"CredentialsSignin\",\n    \"OAuthAccountNotLinked\",\n    \"OAuthCallbackError\",\n    \"AccessDenied\",\n    \"Verification\",\n    \"MissingCSRF\",\n    \"AccountNotLinked\",\n    \"WebAuthnVerificationError\",\n]);\n/**\n * Used to only allow sending a certain subset of errors to the client.\n * Errors are always logged on the server, but to prevent leaking sensitive information,\n * only a subset of errors are sent to the client as-is.\n * @internal\n */\nexport function isClientError(error) {\n    if (error instanceof AuthError)\n        return clientErrors.has(error.type);\n    return false;\n}\n/**\n * Thrown when multiple providers have `enableConditionalUI` set to `true`.\n * Only one provider can have this option enabled at a time.\n * @noInheritDoc\n */\nexport class DuplicateConditionalUI extends AuthError {\n}\nDuplicateConditionalUI.type = \"DuplicateConditionalUI\";\n/**\n * Thrown when a WebAuthn provider has `enableConditionalUI` set to `true` but no formField has `webauthn` in its autocomplete param.\n *\n * The `webauthn` autocomplete param is required for conditional UI to work.\n * @noInheritDoc\n */\nexport class MissingWebAuthnAutocomplete extends AuthError {\n}\nMissingWebAuthnAutocomplete.type = \"MissingWebAuthnAutocomplete\";\n/**\n * Thrown when a WebAuthn provider fails to verify a client response.\n * @noInheritDoc\n */\nexport class WebAuthnVerificationError extends AuthError {\n}\nWebAuthnVerificationError.type = \"WebAuthnVerificationError\";\n/**\n * Thrown when an Email address is already associated with an account\n * but the user is trying an account that is not linked to it.\n *\n * For security reasons, Auth.js does not automatically link accounts to existing accounts if the user is not signed in.\n * @noInheritDoc\n */\nexport class AccountNotLinked extends SignInError {\n}\nAccountNotLinked.type = \"AccountNotLinked\";\n/**\n * Thrown when an experimental feature is used but not enabled.\n * @noInheritDoc\n */\nexport class ExperimentalFeatureNotEnabled extends AuthError {\n}\nExperimentalFeatureNotEnabled.type = \"ExperimentalFeatureNotEnabled\";\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACM,MAAM,kBAAkB;IAC3B,cAAc,GACd,YAAY,OAAO,EAAE,YAAY,CAAE;YAoB/B,0BAAA;QAnBA,IAAI,mBAAmB,OAAO;YAC1B,KAAK,CAAC,WAAW;gBACb,OAAO;oBAAE,KAAK;oBAAS,GAAG,QAAQ,KAAK;oBAAE,GAAG,YAAY;gBAAC;YAC7D;QACJ,OACK,IAAI,OAAO,YAAY,UAAU;YAClC,IAAI,wBAAwB,OAAO;gBAC/B,eAAe;oBAAE,KAAK;oBAAc,GAAG,aAAa,KAAK;gBAAC;YAC9D;YACA,KAAK,CAAC,SAAS;QACnB,OACK;YACD,KAAK,CAAC,WAAW;QACrB;QACA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI;YAErB;QADZ,uEAAuE;QACvE,IAAI,CAAC,IAAI,GAAG,CAAA,yBAAA,IAAI,CAAC,WAAW,CAAC,IAAI,cAArB,oCAAA,yBAAyB;YAEzB;QADZ,uEAAuE;QACvE,IAAI,CAAC,IAAI,GAAG,CAAA,yBAAA,IAAI,CAAC,WAAW,CAAC,IAAI,cAArB,oCAAA,yBAAyB;SACrC,2BAAA,CAAA,SAAA,OAAM,iBAAiB,cAAvB,+CAAA,8BAAA,QAA0B,IAAI,EAAE,IAAI,CAAC,WAAW;QAChD,MAAM,MAAM,AAAC,6BAAoD,OAAxB,IAAI,CAAC,IAAI,CAAC,WAAW;QAC9D,IAAI,CAAC,OAAO,IAAI,AAAC,GAA0C,OAAxC,IAAI,CAAC,OAAO,GAAG,OAAO,IAAG,iBAAmB,OAAJ;IAC/D;AACJ;AAKO,MAAM,oBAAoB;AACjC;AACA,cAAc,GACd,YAAY,IAAI,GAAG;AAeZ,MAAM,qBAAqB;AAClC;AACA,aAAa,IAAI,GAAG;AAMb,MAAM,qBAAqB;AAClC;AACA,aAAa,IAAI,GAAG;AAyCb,MAAM,2BAA2B;AACxC;AACA,mBAAmB,IAAI,GAAG;AAUnB,MAAM,sBAAsB;AACnC;AACA,cAAc,IAAI,GAAG;AAUd,MAAM,mBAAmB;AAChC;AACA,WAAW,IAAI,GAAG;AAYX,MAAM,2BAA2B;AACxC;AACA,mBAAmB,IAAI,GAAG;AAQnB,MAAM,0BAA0B;IACnC,aAAc;QACV,KAAK,IAAI;QACT;;;;;;;;;;SAUC,GACD,IAAI,CAAC,IAAI,GAAG;IAChB;AACJ;AACA,kBAAkB,IAAI,GAAG;AAQlB,MAAM,yBAAyB;AACtC;AACA,iBAAiB,IAAI,GAAG;AAQjB,MAAM,qBAAqB;AAClC;AACA,aAAa,IAAI,GAAG;AAab,MAAM,wBAAwB;AACrC;AACA,gBAAgB,IAAI,GAAG;AAShB,MAAM,uBAAuB;AACpC;AACA,eAAe,IAAI,GAAG;AASf,MAAM,8BAA8B;AAC3C;AACA,sBAAsB,IAAI,GAAG;AAQtB,MAAM,yBAAyB;AACtC;AACA,iBAAiB,IAAI,GAAG;AAejB,MAAM,sBAAsB;AACnC;AACA,cAAc,IAAI,GAAG;AAcd,MAAM,8BAA8B;AAC3C;AACA,sBAAsB,IAAI,GAAG;AAQtB,MAAM,2BAA2B;AACxC;AACA,mBAAmB,IAAI,GAAG;AAOnB,MAAM,+BAA+B;AAC5C;AACA,uBAAuB,IAAI,GAAG;AASvB,MAAM,0BAA0B;AACvC;AACA,kBAAkB,IAAI,GAAG;AAiBlB,MAAM,yBAAyB;AACtC;AACA,iBAAiB,IAAI,GAAG;AAYjB,MAAM,yBAAyB;AACtC;AACA,iBAAiB,IAAI,GAAG;AAWjB,MAAM,qBAAqB;AAClC;AACA,aAAa,IAAI,GAAG;AAOb,MAAM,sBAAsB;AACnC;AACA,cAAc,IAAI,GAAG;AAOd,MAAM,4BAA4B;AACzC;AACA,oBAAoB,IAAI,GAAG;AAKpB,MAAM,wBAAwB;AACrC;AACA,gBAAgB,IAAI,GAAG;AAahB,MAAM,sBAAsB;AACnC;AACA,cAAc,IAAI,GAAG;AAOd,MAAM,qBAAqB;AAClC;AACA,aAAa,IAAI,GAAG;AAYb,MAAM,oBAAoB;AACjC;AACA,YAAY,IAAI,GAAG;AACnB,MAAM,eAAe,IAAI,IAAI;IACzB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AAOM,SAAS,cAAc,KAAK;IAC/B,IAAI,iBAAiB,WACjB,OAAO,aAAa,GAAG,CAAC,MAAM,IAAI;IACtC,OAAO;AACX;AAMO,MAAM,+BAA+B;AAC5C;AACA,uBAAuB,IAAI,GAAG;AAOvB,MAAM,oCAAoC;AACjD;AACA,4BAA4B,IAAI,GAAG;AAK5B,MAAM,kCAAkC;AAC/C;AACA,0BAA0B,IAAI,GAAG;AAQ1B,MAAM,yBAAyB;AACtC;AACA,iBAAiB,IAAI,GAAG;AAKjB,MAAM,sCAAsC;AACnD;AACA,8BAA8B,IAAI,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 686, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/next-auth/lib/client.js"], "sourcesContent": ["\"use client\";\nimport * as React from \"react\";\nimport { AuthError } from \"@auth/core/errors\";\n/** @todo */\nclass ClientFetchError extends AuthError {\n}\n/** @todo */\nexport class ClientSessionError extends AuthError {\n}\n// ------------------------ Internal ------------------------\n/**\n * If passed 'appContext' via getInitialProps() in _app.js\n * then get the req object from ctx and use that for the\n * req value to allow `fetchData` to\n * work seemlessly in getInitialProps() on server side\n * pages *and* in _app.js.\n * @internal\n */\nexport async function fetchData(path, __NEXTAUTH, logger, req = {}) {\n    const url = `${apiBaseUrl(__NEXTAUTH)}/${path}`;\n    try {\n        const options = {\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...(req?.headers?.cookie ? { cookie: req.headers.cookie } : {}),\n            },\n        };\n        if (req?.body) {\n            options.body = JSON.stringify(req.body);\n            options.method = \"POST\";\n        }\n        const res = await fetch(url, options);\n        const data = await res.json();\n        if (!res.ok)\n            throw data;\n        return data;\n    }\n    catch (error) {\n        logger.error(new ClientFetchError(error.message, error));\n        return null;\n    }\n}\n/** @internal */\nexport function apiBaseUrl(__NEXTAUTH) {\n    if (typeof window === \"undefined\") {\n        // Return absolute path when called server side\n        return `${__NEXTAUTH.baseUrlServer}${__NEXTAUTH.basePathServer}`;\n    }\n    // Return relative path when called client side\n    return __NEXTAUTH.basePath;\n}\n/** @internal  */\nexport function useOnline() {\n    const [isOnline, setIsOnline] = React.useState(typeof navigator !== \"undefined\" ? navigator.onLine : false);\n    const setOnline = () => setIsOnline(true);\n    const setOffline = () => setIsOnline(false);\n    React.useEffect(() => {\n        window.addEventListener(\"online\", setOnline);\n        window.addEventListener(\"offline\", setOffline);\n        return () => {\n            window.removeEventListener(\"online\", setOnline);\n            window.removeEventListener(\"offline\", setOffline);\n        };\n    }, []);\n    return isOnline;\n}\n/**\n * Returns the number of seconds elapsed since January 1, 1970 00:00:00 UTC.\n * @internal\n */\nexport function now() {\n    return Math.floor(Date.now() / 1000);\n}\n/**\n * Returns an `URL` like object to make requests/redirects from server-side\n * @internal\n */\nexport function parseUrl(url) {\n    const defaultUrl = new URL(\"http://localhost:3000/api/auth\");\n    if (url && !url.startsWith(\"http\")) {\n        url = `https://${url}`;\n    }\n    const _url = new URL(url || defaultUrl);\n    const path = (_url.pathname === \"/\" ? defaultUrl.pathname : _url.pathname)\n        // Remove trailing slash\n        .replace(/\\/$/, \"\");\n    const base = `${_url.origin}${path}`;\n    return {\n        origin: _url.origin,\n        host: _url.host,\n        path,\n        base,\n        toString: () => base,\n    };\n}\n"], "names": [], "mappings": ";;;;;;;;AACA;AACA;AAFA;;;AAGA,UAAU,GACV,MAAM,yBAAyB,2IAAA,CAAA,YAAS;AACxC;AAEO,MAAM,2BAA2B,2IAAA,CAAA,YAAS;AACjD;AAUO,eAAe,UAAU,IAAI,EAAE,UAAU,EAAE,MAAM;QAAE,MAAA,iEAAM,CAAC;IAC7D,MAAM,MAAM,AAAC,GAA4B,OAA1B,WAAW,aAAY,KAAQ,OAAL;IACzC,IAAI;YAIY;QAHZ,MAAM,UAAU;YACZ,SAAS;gBACL,gBAAgB;gBAChB,GAAI,CAAA,gBAAA,2BAAA,eAAA,IAAK,OAAO,cAAZ,mCAAA,aAAc,MAAM,IAAG;oBAAE,QAAQ,IAAI,OAAO,CAAC,MAAM;gBAAC,IAAI,CAAC,CAAC;YAClE;QACJ;QACA,IAAI,gBAAA,0BAAA,IAAK,IAAI,EAAE;YACX,QAAQ,IAAI,GAAG,KAAK,SAAS,CAAC,IAAI,IAAI;YACtC,QAAQ,MAAM,GAAG;QACrB;QACA,MAAM,MAAM,MAAM,MAAM,KAAK;QAC7B,MAAM,OAAO,MAAM,IAAI,IAAI;QAC3B,IAAI,CAAC,IAAI,EAAE,EACP,MAAM;QACV,OAAO;IACX,EACA,OAAO,OAAO;QACV,OAAO,KAAK,CAAC,IAAI,iBAAiB,MAAM,OAAO,EAAE;QACjD,OAAO;IACX;AACJ;AAEO,SAAS,WAAW,UAAU;IACjC,IAAI,OAAO,WAAW,aAAa;QAC/B,+CAA+C;QAC/C,OAAO,AAAC,GAA6B,OAA3B,WAAW,aAAa,EAA6B,OAA1B,WAAW,cAAc;IAClE;IACA,+CAA+C;IAC/C,OAAO,WAAW,QAAQ;AAC9B;AAEO,SAAS;IACZ,MAAM,CAAC,UAAU,YAAY,GAAG,6JAAA,CAAA,WAAc,CAAC,OAAO,cAAc,cAAc,UAAU,MAAM,GAAG;IACrG,MAAM,YAAY,IAAM,YAAY;IACpC,MAAM,aAAa,IAAM,YAAY;IACrC,6JAAA,CAAA,YAAe;+BAAC;YACZ,OAAO,gBAAgB,CAAC,UAAU;YAClC,OAAO,gBAAgB,CAAC,WAAW;YACnC;uCAAO;oBACH,OAAO,mBAAmB,CAAC,UAAU;oBACrC,OAAO,mBAAmB,CAAC,WAAW;gBAC1C;;QACJ;8BAAG,EAAE;IACL,OAAO;AACX;AAKO,SAAS;IACZ,OAAO,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;AACnC;AAKO,SAAS,SAAS,GAAG;IACxB,MAAM,aAAa,IAAI,IAAI;IAC3B,IAAI,OAAO,CAAC,IAAI,UAAU,CAAC,SAAS;QAChC,MAAM,AAAC,WAAc,OAAJ;IACrB;IACA,MAAM,OAAO,IAAI,IAAI,OAAO;IAC5B,MAAM,OAAO,CAAC,KAAK,QAAQ,KAAK,MAAM,WAAW,QAAQ,GAAG,KAAK,QAAQ,CACrE,wBAAwB;KACvB,OAAO,CAAC,OAAO;IACpB,MAAM,OAAO,AAAC,GAAgB,OAAd,KAAK,MAAM,EAAQ,OAAL;IAC9B,OAAO;QACH,QAAQ,KAAK,MAAM;QACnB,MAAM,KAAK,IAAI;QACf;QACA;QACA,UAAU,IAAM;IACpB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 779, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/next-auth/react.js"], "sourcesContent": ["/**\n *\n * NextAuth.js is the official integration of Auth.js for Next.js applications. It supports both\n * [Client Components](https://nextjs.org/docs/app/building-your-application/rendering/client-components) and the\n * [Pages Router](https://nextjs.org/docs/pages). It includes methods for signing in, signing out, hooks, and a React\n * Context provider to wrap your application and make session data available anywhere.\n *\n * For use in [Server Actions](https://nextjs.org/docs/app/api-reference/functions/server-actions), check out [these methods](https://authjs.dev/guides/upgrade-to-v5#methods)\n *\n * @module react\n */\n\"use client\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport * as React from \"react\";\nimport { apiBaseUrl, ClientSessionError, fetchData, now, parseUrl, useOnline, } from \"./lib/client.js\";\n// This behaviour mirrors the default behaviour for getting the site name that\n// happens server side in server/index.js\n// 1. An empty value is legitimate when the code is being invoked client side as\n//    relative URLs are valid in that context and so defaults to empty.\n// 2. When invoked server side the value is picked up from an environment\n//    variable and defaults to 'http://localhost:3000'.\nexport const __NEXTAUTH = {\n    baseUrl: parseUrl(process.env.NEXTAUTH_URL ?? process.env.VERCEL_URL).origin,\n    basePath: parseUrl(process.env.NEXTAUTH_URL).path,\n    baseUrlServer: parseUrl(process.env.NEXTAUTH_URL_INTERNAL ??\n        process.env.NEXTAUTH_URL ??\n        process.env.VERCEL_URL).origin,\n    basePathServer: parseUrl(process.env.NEXTAUTH_URL_INTERNAL ?? process.env.NEXTAUTH_URL).path,\n    _lastSync: 0,\n    _session: undefined,\n    _getSession: () => { },\n};\n// https://github.com/nextauthjs/next-auth/pull/10762\nlet broadcastChannel = null;\nfunction getNewBroadcastChannel() {\n    if (typeof BroadcastChannel === \"undefined\") {\n        return {\n            postMessage: () => { },\n            addEventListener: () => { },\n            removeEventListener: () => { },\n            name: \"next-auth\",\n            onmessage: null,\n            onmessageerror: null,\n            close: () => { },\n            dispatchEvent: () => false,\n        };\n    }\n    return new BroadcastChannel(\"next-auth\");\n}\nfunction broadcast() {\n    if (broadcastChannel === null) {\n        broadcastChannel = getNewBroadcastChannel();\n    }\n    return broadcastChannel;\n}\n// TODO:\nconst logger = {\n    debug: console.debug,\n    error: console.error,\n    warn: console.warn,\n};\nexport const SessionContext = React.createContext?.(undefined);\n/**\n * React Hook that gives you access to the logged in user's session data and lets you modify it.\n *\n * :::info\n * `useSession` is for client-side use only and when using [Next.js App Router (`app/`)](https://nextjs.org/blog/next-13-4#nextjs-app-router) you should prefer the `auth()` export.\n * :::\n */\nexport function useSession(options) {\n    if (!SessionContext) {\n        throw new Error(\"React Context is unavailable in Server Components\");\n    }\n    // @ts-expect-error Satisfy TS if branch on line below\n    const value = React.useContext(SessionContext);\n    if (!value && process.env.NODE_ENV !== \"production\") {\n        throw new Error(\"[next-auth]: `useSession` must be wrapped in a <SessionProvider />\");\n    }\n    const { required, onUnauthenticated } = options ?? {};\n    const requiredAndNotLoading = required && value.status === \"unauthenticated\";\n    React.useEffect(() => {\n        if (requiredAndNotLoading) {\n            const url = `${__NEXTAUTH.basePath}/signin?${new URLSearchParams({\n                error: \"SessionRequired\",\n                callbackUrl: window.location.href,\n            })}`;\n            if (onUnauthenticated)\n                onUnauthenticated();\n            else\n                window.location.href = url;\n        }\n    }, [requiredAndNotLoading, onUnauthenticated]);\n    if (requiredAndNotLoading) {\n        return {\n            data: value.data,\n            update: value.update,\n            status: \"loading\",\n        };\n    }\n    return value;\n}\nexport async function getSession(params) {\n    const session = await fetchData(\"session\", __NEXTAUTH, logger, params);\n    if (params?.broadcast ?? true) {\n        // https://github.com/nextauthjs/next-auth/pull/11470\n        getNewBroadcastChannel().postMessage({\n            event: \"session\",\n            data: { trigger: \"getSession\" },\n        });\n    }\n    return session;\n}\n/**\n * Returns the current Cross-Site Request Forgery Token (CSRF Token)\n * required to make requests that changes state. (e.g. signing in or out, or updating the session).\n *\n * [CSRF Prevention: Double Submit Cookie](https://cheatsheetseries.owasp.org/cheatsheets/Cross-Site_Request_Forgery_Prevention_Cheat_Sheet.html#double-submit-cookie)\n */\nexport async function getCsrfToken() {\n    const response = await fetchData(\"csrf\", __NEXTAUTH, logger);\n    return response?.csrfToken ?? \"\";\n}\nexport async function getProviders() {\n    return fetchData(\"providers\", __NEXTAUTH, logger);\n}\nexport async function signIn(provider, options, authorizationParams) {\n    const { callbackUrl, ...rest } = options ?? {};\n    const { redirect = true, redirectTo = callbackUrl ?? window.location.href, ...signInParams } = rest;\n    const baseUrl = apiBaseUrl(__NEXTAUTH);\n    const providers = await getProviders();\n    if (!providers) {\n        const url = `${baseUrl}/error`;\n        window.location.href = url;\n        return; // TODO: Return error if `redirect: false`\n    }\n    if (!provider || !providers[provider]) {\n        const url = `${baseUrl}/signin?${new URLSearchParams({\n            callbackUrl: redirectTo,\n        })}`;\n        window.location.href = url;\n        return; // TODO: Return error if `redirect: false`\n    }\n    const providerType = providers[provider].type;\n    if (providerType === \"webauthn\") {\n        // TODO: Add docs link with explanation\n        throw new TypeError([\n            `Provider id \"${provider}\" refers to a WebAuthn provider.`,\n            'Please use `import { signIn } from \"next-auth/webauthn\"` instead.',\n        ].join(\"\\n\"));\n    }\n    const signInUrl = `${baseUrl}/${providerType === \"credentials\" ? \"callback\" : \"signin\"}/${provider}`;\n    const csrfToken = await getCsrfToken();\n    const res = await fetch(`${signInUrl}?${new URLSearchParams(authorizationParams)}`, {\n        method: \"post\",\n        headers: {\n            \"Content-Type\": \"application/x-www-form-urlencoded\",\n            \"X-Auth-Return-Redirect\": \"1\",\n        },\n        body: new URLSearchParams({\n            ...signInParams,\n            csrfToken,\n            callbackUrl: redirectTo,\n        }),\n    });\n    const data = await res.json();\n    if (redirect) {\n        const url = data.url ?? redirectTo;\n        window.location.href = url;\n        // If url contains a hash, the browser does not reload the page. We reload manually\n        if (url.includes(\"#\"))\n            window.location.reload();\n        return;\n    }\n    const error = new URL(data.url).searchParams.get(\"error\") ?? undefined;\n    const code = new URL(data.url).searchParams.get(\"code\") ?? undefined;\n    if (res.ok) {\n        await __NEXTAUTH._getSession({ event: \"storage\" });\n    }\n    return {\n        error,\n        code,\n        status: res.status,\n        ok: res.ok,\n        url: error ? null : data.url,\n    };\n}\nexport async function signOut(options) {\n    const { redirect = true, redirectTo = options?.callbackUrl ?? window.location.href, } = options ?? {};\n    const baseUrl = apiBaseUrl(__NEXTAUTH);\n    const csrfToken = await getCsrfToken();\n    const res = await fetch(`${baseUrl}/signout`, {\n        method: \"post\",\n        headers: {\n            \"Content-Type\": \"application/x-www-form-urlencoded\",\n            \"X-Auth-Return-Redirect\": \"1\",\n        },\n        body: new URLSearchParams({ csrfToken, callbackUrl: redirectTo }),\n    });\n    const data = await res.json();\n    broadcast().postMessage({ event: \"session\", data: { trigger: \"signout\" } });\n    if (redirect) {\n        const url = data.url ?? redirectTo;\n        window.location.href = url;\n        // If url contains a hash, the browser does not reload the page. We reload manually\n        if (url.includes(\"#\"))\n            window.location.reload();\n        return;\n    }\n    await __NEXTAUTH._getSession({ event: \"storage\" });\n    return data;\n}\n/**\n * [React Context](https://react.dev/learn/passing-data-deeply-with-context) provider to wrap the app (`pages/`) to make session data available anywhere.\n *\n * When used, the session state is automatically synchronized across all open tabs/windows and they are all updated whenever they gain or lose focus\n * or the state changes (e.g. a user signs in or out) when {@link SessionProviderProps.refetchOnWindowFocus} is `true`.\n *\n * :::info\n * `SessionProvider` is for client-side use only and when using [Next.js App Router (`app/`)](https://nextjs.org/blog/next-13-4#nextjs-app-router) you should prefer the `auth()` export.\n * :::\n */\nexport function SessionProvider(props) {\n    if (!SessionContext) {\n        throw new Error(\"React Context is unavailable in Server Components\");\n    }\n    const { children, basePath, refetchInterval, refetchWhenOffline } = props;\n    if (basePath)\n        __NEXTAUTH.basePath = basePath;\n    /**\n     * If session was `null`, there was an attempt to fetch it,\n     * but it failed, but we still treat it as a valid initial value.\n     */\n    const hasInitialSession = props.session !== undefined;\n    /** If session was passed, initialize as already synced */\n    __NEXTAUTH._lastSync = hasInitialSession ? now() : 0;\n    const [session, setSession] = React.useState(() => {\n        if (hasInitialSession)\n            __NEXTAUTH._session = props.session;\n        return props.session;\n    });\n    /** If session was passed, initialize as not loading */\n    const [loading, setLoading] = React.useState(!hasInitialSession);\n    React.useEffect(() => {\n        __NEXTAUTH._getSession = async ({ event } = {}) => {\n            try {\n                const storageEvent = event === \"storage\";\n                // We should always update if we don't have a client session yet\n                // or if there are events from other tabs/windows\n                if (storageEvent || __NEXTAUTH._session === undefined) {\n                    __NEXTAUTH._lastSync = now();\n                    __NEXTAUTH._session = await getSession({\n                        broadcast: !storageEvent,\n                    });\n                    setSession(__NEXTAUTH._session);\n                    return;\n                }\n                if (\n                // If there is no time defined for when a session should be considered\n                // stale, then it's okay to use the value we have until an event is\n                // triggered which updates it\n                !event ||\n                    // If the client doesn't have a session then we don't need to call\n                    // the server to check if it does (if they have signed in via another\n                    // tab or window that will come through as a \"stroage\" event\n                    // event anyway)\n                    __NEXTAUTH._session === null ||\n                    // Bail out early if the client session is not stale yet\n                    now() < __NEXTAUTH._lastSync) {\n                    return;\n                }\n                // An event or session staleness occurred, update the client session.\n                __NEXTAUTH._lastSync = now();\n                __NEXTAUTH._session = await getSession();\n                setSession(__NEXTAUTH._session);\n            }\n            catch (error) {\n                logger.error(new ClientSessionError(error.message, error));\n            }\n            finally {\n                setLoading(false);\n            }\n        };\n        __NEXTAUTH._getSession();\n        return () => {\n            __NEXTAUTH._lastSync = 0;\n            __NEXTAUTH._session = undefined;\n            __NEXTAUTH._getSession = () => { };\n        };\n    }, []);\n    React.useEffect(() => {\n        const handle = () => __NEXTAUTH._getSession({ event: \"storage\" });\n        // Listen for storage events and update session if event fired from\n        // another window (but suppress firing another event to avoid a loop)\n        // Fetch new session data but tell it to not to fire another event to\n        // avoid an infinite loop.\n        // Note: We could pass session data through and do something like\n        // `setData(message.data)` but that can cause problems depending\n        // on how the session object is being used in the client; it is\n        // more robust to have each window/tab fetch it's own copy of the\n        // session object rather than share it across instances.\n        broadcast().addEventListener(\"message\", handle);\n        return () => broadcast().removeEventListener(\"message\", handle);\n    }, []);\n    React.useEffect(() => {\n        const { refetchOnWindowFocus = true } = props;\n        // Listen for when the page is visible, if the user switches tabs\n        // and makes our tab visible again, re-fetch the session, but only if\n        // this feature is not disabled.\n        const visibilityHandler = () => {\n            if (refetchOnWindowFocus && document.visibilityState === \"visible\")\n                __NEXTAUTH._getSession({ event: \"visibilitychange\" });\n        };\n        document.addEventListener(\"visibilitychange\", visibilityHandler, false);\n        return () => document.removeEventListener(\"visibilitychange\", visibilityHandler, false);\n    }, [props.refetchOnWindowFocus]);\n    const isOnline = useOnline();\n    // TODO: Flip this behavior in next major version\n    const shouldRefetch = refetchWhenOffline !== false || isOnline;\n    React.useEffect(() => {\n        if (refetchInterval && shouldRefetch) {\n            const refetchIntervalTimer = setInterval(() => {\n                if (__NEXTAUTH._session) {\n                    __NEXTAUTH._getSession({ event: \"poll\" });\n                }\n            }, refetchInterval * 1000);\n            return () => clearInterval(refetchIntervalTimer);\n        }\n    }, [refetchInterval, shouldRefetch]);\n    const value = React.useMemo(() => ({\n        data: session,\n        status: loading\n            ? \"loading\"\n            : session\n                ? \"authenticated\"\n                : \"unauthenticated\",\n        async update(data) {\n            if (loading)\n                return;\n            setLoading(true);\n            const newSession = await fetchData(\"session\", __NEXTAUTH, logger, typeof data === \"undefined\"\n                ? undefined\n                : { body: { csrfToken: await getCsrfToken(), data } });\n            setLoading(false);\n            if (newSession) {\n                setSession(newSession);\n                broadcast().postMessage({\n                    event: \"session\",\n                    data: { trigger: \"getSession\" },\n                });\n            }\n            return newSession;\n        },\n    }), [session, loading]);\n    return (\n    // @ts-expect-error\n    _jsx(SessionContext.Provider, { value: value, children: children }));\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC;;;;;;;;;;;AAYqB;AAVtB;AACA;AACA;AAHA;IAkD8B;;;;IAvCR,2BAEM,oCAAA,MAGC;AANtB,MAAM,aAAa;IACtB,SAAS,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,4BAAA,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,YAAY,cAAxB,uCAAA,4BAA4B,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,UAAU,EAAE,MAAM;IAC5E,UAAU,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD,EAAE,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI;IACjD,eAAe,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,OAAA,CAAA,qCAAA,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,qBAAqB,cAAjC,gDAAA,qCACpB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,YAAY,cADJ,kBAAA,OAEpB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,UAAU,EAAE,MAAM;IAClC,gBAAgB,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,sCAAA,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,qBAAqB,cAAjC,iDAAA,sCAAqC,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI;IAC5F,WAAW;IACX,UAAU;IACV,aAAa,KAAQ;AACzB;AACA,qDAAqD;AACrD,IAAI,mBAAmB;AACvB,SAAS;IACL,IAAI,OAAO,qBAAqB,aAAa;QACzC,OAAO;YACH,aAAa,KAAQ;YACrB,kBAAkB,KAAQ;YAC1B,qBAAqB,KAAQ;YAC7B,MAAM;YACN,WAAW;YACX,gBAAgB;YAChB,OAAO,KAAQ;YACf,eAAe,IAAM;QACzB;IACJ;IACA,OAAO,IAAI,iBAAiB;AAChC;AACA,SAAS;IACL,IAAI,qBAAqB,MAAM;QAC3B,mBAAmB;IACvB;IACA,OAAO;AACX;AACA,QAAQ;AACR,MAAM,SAAS;IACX,OAAO,QAAQ,KAAK;IACpB,OAAO,QAAQ,KAAK;IACpB,MAAM,QAAQ,IAAI;AACtB;AACO,MAAM,kBAAiB,uBAAA,8JAAM,aAAa,cAAnB,2CAAA,0BAAA,+JAAsB;AAQ7C,SAAS,WAAW,OAAO;IAC9B,IAAI,CAAC,gBAAgB;QACjB,MAAM,IAAI,MAAM;IACpB;IACA,sDAAsD;IACtD,MAAM,QAAQ,8JAAM,UAAU,CAAC;IAC/B,IAAI,CAAC,SAAS,oDAAyB,cAAc;QACjD,MAAM,IAAI,MAAM;IACpB;IACA,MAAM,EAAE,QAAQ,EAAE,iBAAiB,EAAE,GAAG,oBAAA,qBAAA,UAAW,CAAC;IACpD,MAAM,wBAAwB,YAAY,MAAM,MAAM,KAAK;IAC3D,8JAAM,SAAS;gCAAC;YACZ,IAAI,uBAAuB;gBACvB,MAAM,MAAM,AAAC,GAAgC,OAA9B,WAAW,QAAQ,EAAC,YAGhC,OAH0C,IAAI,gBAAgB;oBAC7D,OAAO;oBACP,aAAa,OAAO,QAAQ,CAAC,IAAI;gBACrC;gBACA,IAAI,mBACA;qBAEA,OAAO,QAAQ,CAAC,IAAI,GAAG;YAC/B;QACJ;+BAAG;QAAC;QAAuB;KAAkB;IAC7C,IAAI,uBAAuB;QACvB,OAAO;YACH,MAAM,MAAM,IAAI;YAChB,QAAQ,MAAM,MAAM;YACpB,QAAQ;QACZ;IACJ;IACA,OAAO;AACX;AACO,eAAe,WAAW,MAAM;IACnC,MAAM,UAAU,MAAM,CAAA,GAAA,gJAAA,CAAA,YAAS,AAAD,EAAE,WAAW,YAAY,QAAQ;QAC3D;IAAJ,IAAI,CAAA,oBAAA,mBAAA,6BAAA,OAAQ,SAAS,cAAjB,+BAAA,oBAAqB,MAAM;QAC3B,qDAAqD;QACrD,yBAAyB,WAAW,CAAC;YACjC,OAAO;YACP,MAAM;gBAAE,SAAS;YAAa;QAClC;IACJ;IACA,OAAO;AACX;AAOO,eAAe;IAClB,MAAM,WAAW,MAAM,CAAA,GAAA,gJAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,YAAY;QAC9C;IAAP,OAAO,CAAA,sBAAA,qBAAA,+BAAA,SAAU,SAAS,cAAnB,iCAAA,sBAAuB;AAClC;AACO,eAAe;IAClB,OAAO,CAAA,GAAA,gJAAA,CAAA,YAAS,AAAD,EAAE,aAAa,YAAY;AAC9C;AACO,eAAe,OAAO,QAAQ,EAAE,OAAO,EAAE,mBAAmB;IAC/D,MAAM,EAAE,WAAW,EAAE,GAAG,MAAM,GAAG,oBAAA,qBAAA,UAAW,CAAC;IAC7C,MAAM,EAAE,WAAW,IAAI,EAAE,aAAa,wBAAA,yBAAA,cAAe,OAAO,QAAQ,CAAC,IAAI,EAAE,GAAG,cAAc,GAAG;IAC/F,MAAM,UAAU,CAAA,GAAA,gJAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,MAAM,YAAY,MAAM;IACxB,IAAI,CAAC,WAAW;QACZ,MAAM,MAAM,AAAC,GAAU,OAAR,SAAQ;QACvB,OAAO,QAAQ,CAAC,IAAI,GAAG;QACvB,QAAQ,0CAA0C;IACtD;IACA,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,EAAE;QACnC,MAAM,MAAM,AAAC,GAAoB,OAAlB,SAAQ,YAEpB,OAF8B,IAAI,gBAAgB;YACjD,aAAa;QACjB;QACA,OAAO,QAAQ,CAAC,IAAI,GAAG;QACvB,QAAQ,0CAA0C;IACtD;IACA,MAAM,eAAe,SAAS,CAAC,SAAS,CAAC,IAAI;IAC7C,IAAI,iBAAiB,YAAY;QAC7B,uCAAuC;QACvC,MAAM,IAAI,UAAU;YACf,gBAAwB,OAAT,UAAS;YACzB;SACH,CAAC,IAAI,CAAC;IACX;IACA,MAAM,YAAY,AAAC,GAAa,OAAX,SAAQ,KAA6D,OAA1D,iBAAiB,gBAAgB,aAAa,UAAS,KAAY,OAAT;IAC1F,MAAM,YAAY,MAAM;IACxB,MAAM,MAAM,MAAM,MAAM,AAAC,GAAe,OAAb,WAAU,KAA4C,OAAzC,IAAI,gBAAgB,uBAAwB;QAChF,QAAQ;QACR,SAAS;YACL,gBAAgB;YAChB,0BAA0B;QAC9B;QACA,MAAM,IAAI,gBAAgB;YACtB,GAAG,YAAY;YACf;YACA,aAAa;QACjB;IACJ;IACA,MAAM,OAAO,MAAM,IAAI,IAAI;IAC3B,IAAI,UAAU;YACE;QAAZ,MAAM,MAAM,CAAA,YAAA,KAAK,GAAG,cAAR,uBAAA,YAAY;QACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;QACvB,mFAAmF;QACnF,IAAI,IAAI,QAAQ,CAAC,MACb,OAAO,QAAQ,CAAC,MAAM;QAC1B;IACJ;QACc;IAAd,MAAM,QAAQ,CAAA,oBAAA,IAAI,IAAI,KAAK,GAAG,EAAE,YAAY,CAAC,GAAG,CAAC,sBAAnC,+BAAA,oBAA+C;QAChD;IAAb,MAAM,OAAO,CAAA,qBAAA,IAAI,IAAI,KAAK,GAAG,EAAE,YAAY,CAAC,GAAG,CAAC,qBAAnC,gCAAA,qBAA8C;IAC3D,IAAI,IAAI,EAAE,EAAE;QACR,MAAM,WAAW,WAAW,CAAC;YAAE,OAAO;QAAU;IACpD;IACA,OAAO;QACH;QACA;QACA,QAAQ,IAAI,MAAM;QAClB,IAAI,IAAI,EAAE;QACV,KAAK,QAAQ,OAAO,KAAK,GAAG;IAChC;AACJ;AACO,eAAe,QAAQ,OAAO;QACK;IAAtC,MAAM,EAAE,WAAW,IAAI,EAAE,aAAa,CAAA,uBAAA,oBAAA,8BAAA,QAAS,WAAW,cAApB,kCAAA,uBAAwB,OAAO,QAAQ,CAAC,IAAI,EAAG,GAAG,oBAAA,qBAAA,UAAW,CAAC;IACpG,MAAM,UAAU,CAAA,GAAA,gJAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,MAAM,YAAY,MAAM;IACxB,MAAM,MAAM,MAAM,MAAM,AAAC,GAAU,OAAR,SAAQ,aAAW;QAC1C,QAAQ;QACR,SAAS;YACL,gBAAgB;YAChB,0BAA0B;QAC9B;QACA,MAAM,IAAI,gBAAgB;YAAE;YAAW,aAAa;QAAW;IACnE;IACA,MAAM,OAAO,MAAM,IAAI,IAAI;IAC3B,YAAY,WAAW,CAAC;QAAE,OAAO;QAAW,MAAM;YAAE,SAAS;QAAU;IAAE;IACzE,IAAI,UAAU;YACE;QAAZ,MAAM,MAAM,CAAA,YAAA,KAAK,GAAG,cAAR,uBAAA,YAAY;QACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;QACvB,mFAAmF;QACnF,IAAI,IAAI,QAAQ,CAAC,MACb,OAAO,QAAQ,CAAC,MAAM;QAC1B;IACJ;IACA,MAAM,WAAW,WAAW,CAAC;QAAE,OAAO;IAAU;IAChD,OAAO;AACX;AAWO,SAAS,gBAAgB,KAAK;IACjC,IAAI,CAAC,gBAAgB;QACjB,MAAM,IAAI,MAAM;IACpB;IACA,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,eAAe,EAAE,kBAAkB,EAAE,GAAG;IACpE,IAAI,UACA,WAAW,QAAQ,GAAG;IAC1B;;;KAGC,GACD,MAAM,oBAAoB,MAAM,OAAO,KAAK;IAC5C,wDAAwD,GACxD,WAAW,SAAS,GAAG,oBAAoB,CAAA,GAAA,gJAAA,CAAA,MAAG,AAAD,MAAM;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,8JAAM,QAAQ;oCAAC;YACzC,IAAI,mBACA,WAAW,QAAQ,GAAG,MAAM,OAAO;YACvC,OAAO,MAAM,OAAO;QACxB;;IACA,qDAAqD,GACrD,MAAM,CAAC,SAAS,WAAW,GAAG,8JAAM,QAAQ,CAAC,CAAC;IAC9C,8JAAM,SAAS;qCAAC;YACZ,WAAW,WAAW;6CAAG;wBAAO,EAAE,KAAK,EAAE,oEAAG,CAAC;oBACzC,IAAI;wBACA,MAAM,eAAe,UAAU;wBAC/B,gEAAgE;wBAChE,iDAAiD;wBACjD,IAAI,gBAAgB,WAAW,QAAQ,KAAK,WAAW;4BACnD,WAAW,SAAS,GAAG,CAAA,GAAA,gJAAA,CAAA,MAAG,AAAD;4BACzB,WAAW,QAAQ,GAAG,MAAM,WAAW;gCACnC,WAAW,CAAC;4BAChB;4BACA,WAAW,WAAW,QAAQ;4BAC9B;wBACJ;wBACA,IACA,sEAAsE;wBACtE,mEAAmE;wBACnE,6BAA6B;wBAC7B,CAAC,SACG,kEAAkE;wBAClE,qEAAqE;wBACrE,4DAA4D;wBAC5D,gBAAgB;wBAChB,WAAW,QAAQ,KAAK,QACxB,wDAAwD;wBACxD,CAAA,GAAA,gJAAA,CAAA,MAAG,AAAD,MAAM,WAAW,SAAS,EAAE;4BAC9B;wBACJ;wBACA,qEAAqE;wBACrE,WAAW,SAAS,GAAG,CAAA,GAAA,gJAAA,CAAA,MAAG,AAAD;wBACzB,WAAW,QAAQ,GAAG,MAAM;wBAC5B,WAAW,WAAW,QAAQ;oBAClC,EACA,OAAO,OAAO;wBACV,OAAO,KAAK,CAAC,IAAI,gJAAA,CAAA,qBAAkB,CAAC,MAAM,OAAO,EAAE;oBACvD,SACQ;wBACJ,WAAW;oBACf;gBACJ;;YACA,WAAW,WAAW;YACtB;6CAAO;oBACH,WAAW,SAAS,GAAG;oBACvB,WAAW,QAAQ,GAAG;oBACtB,WAAW,WAAW;qDAAG,KAAQ;;gBACrC;;QACJ;oCAAG,EAAE;IACL,8JAAM,SAAS;qCAAC;YACZ,MAAM;oDAAS,IAAM,WAAW,WAAW,CAAC;wBAAE,OAAO;oBAAU;;YAC/D,mEAAmE;YACnE,qEAAqE;YACrE,qEAAqE;YACrE,0BAA0B;YAC1B,iEAAiE;YACjE,gEAAgE;YAChE,+DAA+D;YAC/D,iEAAiE;YACjE,wDAAwD;YACxD,YAAY,gBAAgB,CAAC,WAAW;YACxC;6CAAO,IAAM,YAAY,mBAAmB,CAAC,WAAW;;QAC5D;oCAAG,EAAE;IACL,8JAAM,SAAS;qCAAC;YACZ,MAAM,EAAE,uBAAuB,IAAI,EAAE,GAAG;YACxC,iEAAiE;YACjE,qEAAqE;YACrE,gCAAgC;YAChC,MAAM;+DAAoB;oBACtB,IAAI,wBAAwB,SAAS,eAAe,KAAK,WACrD,WAAW,WAAW,CAAC;wBAAE,OAAO;oBAAmB;gBAC3D;;YACA,SAAS,gBAAgB,CAAC,oBAAoB,mBAAmB;YACjE;6CAAO,IAAM,SAAS,mBAAmB,CAAC,oBAAoB,mBAAmB;;QACrF;oCAAG;QAAC,MAAM,oBAAoB;KAAC;IAC/B,MAAM,WAAW,CAAA,GAAA,gJAAA,CAAA,YAAS,AAAD;IACzB,iDAAiD;IACjD,MAAM,gBAAgB,uBAAuB,SAAS;IACtD,8JAAM,SAAS;qCAAC;YACZ,IAAI,mBAAmB,eAAe;gBAClC,MAAM,uBAAuB;sEAAY;wBACrC,IAAI,WAAW,QAAQ,EAAE;4BACrB,WAAW,WAAW,CAAC;gCAAE,OAAO;4BAAO;wBAC3C;oBACJ;qEAAG,kBAAkB;gBACrB;iDAAO,IAAM,cAAc;;YAC/B;QACJ;oCAAG;QAAC;QAAiB;KAAc;IACnC,MAAM,QAAQ,8JAAM,OAAO;0CAAC,IAAM,CAAC;gBAC/B,MAAM;gBACN,QAAQ,UACF,YACA,UACI,kBACA;gBACV,MAAM,QAAO,IAAI;oBACb,IAAI,SACA;oBACJ,WAAW;oBACX,MAAM,aAAa,MAAM,CAAA,GAAA,gJAAA,CAAA,YAAS,AAAD,EAAE,WAAW,YAAY,QAAQ,OAAO,SAAS,cAC5E,YACA;wBAAE,MAAM;4BAAE,WAAW,MAAM;4BAAgB;wBAAK;oBAAE;oBACxD,WAAW;oBACX,IAAI,YAAY;wBACZ,WAAW;wBACX,YAAY,WAAW,CAAC;4BACpB,OAAO;4BACP,MAAM;gCAAE,SAAS;4BAAa;wBAClC;oBACJ;oBACA,OAAO;gBACX;YACJ,CAAC;yCAAG;QAAC;QAAS;KAAQ;IACtB,OACA,mBAAmB;IACnB,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,eAAe,QAAQ,EAAE;QAAE,OAAO;QAAO,UAAU;IAAS;AACrE", "ignoreList": [0], "debugId": null}}]}