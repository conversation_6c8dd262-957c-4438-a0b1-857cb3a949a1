{"version": 3, "sources": [], "sections": [{"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/lib/auth/config.ts"], "sourcesContent": ["import NextAuth from \"next-auth\"\nimport Credentials from \"next-auth/providers/credentials\"\n\n// Mock user data - in production, this would come from a database\nconst users = [\n  {\n    id: \"1\",\n    email: \"<EMAIL>\",\n    password: \"admin123\",\n    name: \"Administrator\",\n    role: \"admin\"\n  },\n  {\n    id: \"2\", \n    email: \"<EMAIL>\",\n    password: \"teacher123\",\n    name: \"Teacher\",\n    role: \"teacher\"\n  }\n]\n\nexport const { handlers, signIn, signOut, auth } = NextAuth({\n  providers: [\n    Credentials({\n      credentials: {\n        email: { label: \"Email\", type: \"email\" },\n        password: { label: \"Password\", type: \"password\" }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = users.find(\n          (user) => user.email === credentials.email && user.password === credentials.password\n        )\n\n        if (user) {\n          return {\n            id: user.id,\n            email: user.email,\n            name: user.name,\n            role: user.role\n          }\n        }\n\n        return null\n      }\n    })\n  ],\n  pages: {\n    signIn: \"/login\"\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!\n        session.user.role = token.role as string\n      }\n      return session\n    }\n  },\n  session: {\n    strategy: \"jwt\"\n  }\n})\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;AACA;AAAA;;;AAEA,kEAAkE;AAClE,MAAM,QAAQ;IACZ;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,MAAM;QACN,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,MAAM;QACN,MAAM;IACR;CACD;AAEM,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,6JAAA,CAAA,UAAQ,AAAD,EAAE;IAC1D,WAAW;QACT,CAAA,GAAA,kKAAA,CAAA,UAAW,AAAD,EAAE;YACV,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,IAAI,CACrB,CAAC,OAAS,KAAK,KAAK,KAAK,YAAY,KAAK,IAAI,KAAK,QAAQ,KAAK,YAAY,QAAQ;gBAGtF,IAAI,MAAM;oBACR,OAAO;wBACL,IAAI,KAAK,EAAE;wBACX,OAAO,KAAK,KAAK;wBACjB,MAAM,KAAK,IAAI;wBACf,MAAM,KAAK,IAAI;oBACjB;gBACF;gBAEA,OAAO;YACT;QACF;KACD;IACD,OAAO;QACL,QAAQ;IACV;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;YACxB;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;YAChC;YACA,OAAO;QACT;IACF;IACA,SAAS;QACP,UAAU;IACZ;AACF"}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/middleware.ts"], "sourcesContent": ["import { auth } from \"@/lib/auth/config\"\nimport { NextResponse } from \"next/server\"\n\nexport default auth((req) => {\n  const { pathname } = req.nextUrl\n  const isLoggedIn = !!req.auth\n\n  // Public routes that don't require authentication\n  const publicRoutes = [\"/login\"]\n  const isPublicRoute = publicRoutes.includes(pathname)\n\n  // If user is not logged in and trying to access protected route\n  if (!isLoggedIn && !isPublicRoute) {\n    return NextResponse.redirect(new URL(\"/login\", req.url))\n  }\n\n  // If user is logged in and trying to access login page\n  if (isLoggedIn && pathname === \"/login\") {\n    return NextResponse.redirect(new URL(\"/dashboard\", req.url))\n  }\n\n  return NextResponse.next()\n})\n\nexport const config = {\n  matcher: [\"/((?!api|_next/static|_next/image|favicon.ico).*)\"]\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;uCAEe,CAAA,GAAA,6HAAA,CAAA,OAAI,AAAD,EAAE,CAAC;IACnB,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,OAAO;IAChC,MAAM,aAAa,CAAC,CAAC,IAAI,IAAI;IAE7B,kDAAkD;IAClD,MAAM,eAAe;QAAC;KAAS;IAC/B,MAAM,gBAAgB,aAAa,QAAQ,CAAC;IAE5C,gEAAgE;IAChE,IAAI,CAAC,cAAc,CAAC,eAAe;QACjC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,IAAI,GAAG;IACxD;IAEA,uDAAuD;IACvD,IAAI,cAAc,aAAa,UAAU;QACvC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,cAAc,IAAI,GAAG;IAC5D;IAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,SAAS;QAAC;KAAoD;AAChE"}}]}