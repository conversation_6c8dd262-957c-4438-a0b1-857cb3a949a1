1:"$Sreact.fragment"
2:I[1321,["493","static/chunks/493-9183a606786a8e5a.js","177","static/chunks/app/layout-b91f0cb18d2a387d.js"],"ThemeProvider"]
3:I[7236,["493","static/chunks/493-9183a606786a8e5a.js","177","static/chunks/app/layout-b91f0cb18d2a387d.js"],"AuthProvider"]
4:I[7555,[],""]
5:I[1295,[],""]
6:I[4364,["277","static/chunks/277-6cd9c283f70fe2a1.js","493","static/chunks/493-9183a606786a8e5a.js","491","static/chunks/491-945cd5538409c224.js","357","static/chunks/357-d104fcae1d10ae74.js","305","static/chunks/app/(dashboard)/layout-71e46c4c83e575b3.js"],"AppSidebar"]
7:I[9633,["277","static/chunks/277-6cd9c283f70fe2a1.js","493","static/chunks/493-9183a606786a8e5a.js","491","static/chunks/491-945cd5538409c224.js","357","static/chunks/357-d104fcae1d10ae74.js","305","static/chunks/app/(dashboard)/layout-71e46c4c83e575b3.js"],"Header"]
13:I[8393,[],""]
:HL["/_next/static/css/9607e0db544c62ae.css","style"]
0:{"P":null,"b":"uMSIbYafsYeq_W1FcyE44","p":"","c":["","dashboard"],"i":false,"f":[[["",{"children":["(dashboard)",{"children":["dashboard",{"children":["__PAGE__",{}]}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/9607e0db544c62ae.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","suppressHydrationWarning":true,"children":["$","body",null,{"className":"__variable_5cfdac __variable_9a8899 antialiased","children":["$","$L2",null,{"attribute":"class","defaultTheme":"system","enableSystem":true,"disableTransitionOnChange":true,"children":["$","$L3",null,{"children":["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]}]}]}]]}],{"children":["(dashboard)",["$","$1","c",{"children":[null,["$","div",null,{"className":"flex h-screen bg-background","children":[["$","aside",null,{"className":"hidden md:block","children":["$","$L6",null,{}]}],["$","div",null,{"className":"flex-1 flex flex-col overflow-hidden","children":[["$","$L7",null,{}],["$","main",null,{"className":"flex-1 overflow-y-auto p-6","children":["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":"$0:f:0:1:1:props:children:1:props:children:props:children:props:children:props:children:props:notFound:0:1:props:style","children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":"$0:f:0:1:1:props:children:1:props:children:props:children:props:children:props:children:props:notFound:0:1:props:children:props:children:1:props:style","children":404}],["$","div",null,{"style":"$0:f:0:1:1:props:children:1:props:children:props:children:props:children:props:children:props:notFound:0:1:props:children:props:children:2:props:style","children":["$","h2",null,{"style":"$0:f:0:1:1:props:children:1:props:children:props:children:props:children:props:children:props:notFound:0:1:props:children:props:children:2:props:children:props:style","children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]]}]]}]]}],{"children":["dashboard",["$","$1","c",{"children":[null,["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","div",null,{"className":"space-y-6","children":[["$","div",null,{"children":[["$","h1",null,{"className":"text-3xl font-bold tracking-tight","children":"Dashboard"}],["$","p",null,{"className":"text-muted-foreground","children":"Welcome to QRSAMS - QR-Code Based Student Attendance and Monitoring System"}]]}],["$","div",null,{"className":"grid gap-4 md:grid-cols-2 lg:grid-cols-4","children":[["$","div",null,{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header auto-rows-min grid-rows-[auto_auto] gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 flex flex-row items-center justify-between space-y-0 pb-2","children":[["$","div",null,{"data-slot":"card-title","className":"text-sm font-medium","children":"Total Students"}],["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-users h-4 w-4 text-muted-foreground","aria-hidden":"true","children":["$L8","$L9","$La","$Lb","$undefined"]}]]}],"$Lc"]}],"$Ld","$Le","$Lf"]}],"$L10"]}],null,"$L11"]}],{},null,false]},null,false]},null,false]},null,false],"$L12",false]],"m":"$undefined","G":["$13",[]],"s":false,"S":true}
15:I[9665,[],"OutletBoundary"]
17:I[4911,[],"AsyncMetadataOutlet"]
19:I[9665,[],"ViewportBoundary"]
1b:I[9665,[],"MetadataBoundary"]
1c:"$Sreact.suspense"
8:["$","path","1yyitq",{"d":"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"}]
9:["$","path","16gr8j",{"d":"M16 3.128a4 4 0 0 1 0 7.744"}]
a:["$","path","kshegd",{"d":"M22 21v-2a4 4 0 0 0-3-3.87"}]
b:["$","circle","nufk8",{"cx":"9","cy":"7","r":"4"}]
c:["$","div",null,{"data-slot":"card-content","className":"px-6","children":[["$","div",null,{"className":"text-2xl font-bold","children":"1,234"}],["$","p",null,{"className":"text-xs text-muted-foreground","children":"+20 from last month"}]]}]
d:["$","div",null,{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header auto-rows-min grid-rows-[auto_auto] gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 flex flex-row items-center justify-between space-y-0 pb-2","children":[["$","div",null,{"data-slot":"card-title","className":"text-sm font-medium","children":"Today's Attendance"}],["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-clipboard-check h-4 w-4 text-muted-foreground","aria-hidden":"true","children":[["$","rect","tgr4d6",{"width":"8","height":"4","x":"8","y":"2","rx":"1","ry":"1"}],["$","path","116196",{"d":"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"}],["$","path","df797q",{"d":"m9 14 2 2 4-4"}],"$undefined"]}]]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":[["$","div",null,{"className":"text-2xl font-bold","children":"89.5%"}],["$","p",null,{"className":"text-xs text-muted-foreground","children":"+2.1% from yesterday"}]]}]]}]
e:["$","div",null,{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header auto-rows-min grid-rows-[auto_auto] gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 flex flex-row items-center justify-between space-y-0 pb-2","children":[["$","div",null,{"data-slot":"card-title","className":"text-sm font-medium","children":"Weekly Average"}],["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-trending-up h-4 w-4 text-muted-foreground","aria-hidden":"true","children":[["$","path","box55l",{"d":"M16 7h6v6"}],["$","path","1t1m79",{"d":"m22 7-8.5 8.5-5-5L2 17"}],"$undefined"]}]]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":[["$","div",null,{"className":"text-2xl font-bold","children":"92.3%"}],["$","p",null,{"className":"text-xs text-muted-foreground","children":"+5.2% from last week"}]]}]]}]
f:["$","div",null,{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header auto-rows-min grid-rows-[auto_auto] gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 flex flex-row items-center justify-between space-y-0 pb-2","children":[["$","div",null,{"data-slot":"card-title","className":"text-sm font-medium","children":"Active Classes"}],["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-calendar h-4 w-4 text-muted-foreground","aria-hidden":"true","children":[["$","path","1cmpym",{"d":"M8 2v4"}],["$","path","4m81vk",{"d":"M16 2v4"}],["$","rect","1hopcy",{"width":"18","height":"18","x":"3","y":"4","rx":"2"}],["$","path","8toen8",{"d":"M3 10h18"}],"$undefined"]}]]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":[["$","div",null,{"className":"text-2xl font-bold","children":"24"}],["$","p",null,{"className":"text-xs text-muted-foreground","children":"8 ongoing sessions"}]]}]]}]
10:["$","div",null,{"className":"grid gap-4 md:grid-cols-2","children":[["$","div",null,{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6","children":[["$","div",null,{"data-slot":"card-title","className":"leading-none font-semibold","children":"Recent Attendance"}],["$","div",null,{"data-slot":"card-description","className":"text-muted-foreground text-sm","children":"Latest student check-ins"}]]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":["$","div",null,{"className":"space-y-4","children":[["$","div","0",{"className":"flex items-center justify-between","children":[["$","div",null,{"children":[["$","p",null,{"className":"text-sm font-medium","children":"John Doe"}],["$","p",null,{"className":"text-xs text-muted-foreground","children":"8:30 AM"}]]}],["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90","children":"present"}]]}],["$","div","1",{"className":"flex items-center justify-between","children":[["$","div",null,{"children":[["$","p",null,{"className":"text-sm font-medium","children":"Jane Smith"}],["$","p",null,{"className":"text-xs text-muted-foreground","children":"8:25 AM"}]]}],["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90","children":"present"}]]}],["$","div","2",{"className":"flex items-center justify-between","children":[["$","div",null,{"children":[["$","p",null,{"className":"text-sm font-medium","children":"Mike Johnson"}],["$","p",null,{"className":"text-xs text-muted-foreground","children":"8:45 AM"}]]}],["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90","children":"late"}]]}],["$","div","3",{"className":"flex items-center justify-between","children":[["$","div",null,{"children":[["$","p",null,{"className":"text-sm font-medium","children":"Sarah Wilson"}],["$","p",null,{"className":"text-xs text-muted-foreground","children":"8:20 AM"}]]}],["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90","children":"present"}]]}]]}]}]]}],"$L14"]}]
11:["$","$L15",null,{"children":["$L16",["$","$L17",null,{"promise":"$@18"}]]}]
12:["$","$1","h",{"children":[null,[["$","$L19",null,{"children":"$L1a"}],null],["$","$L1b",null,{"children":["$","div",null,{"hidden":true,"children":["$","$1c",null,{"fallback":null,"children":"$L1d"}]}]}]]}]
14:["$","div",null,{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6","children":[["$","div",null,{"data-slot":"card-title","className":"leading-none font-semibold","children":"System Status"}],["$","div",null,{"data-slot":"card-description","className":"text-muted-foreground text-sm","children":"Current system information"}]]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":["$","div",null,{"className":"space-y-4","children":[["$","div",null,{"className":"flex items-center justify-between","children":[["$","span",null,{"className":"text-sm","children":"QR Scanner Status"}],["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90","children":"Online"}]]}],["$","div",null,{"className":"flex items-center justify-between","children":[["$","span",null,{"className":"text-sm","children":"Database Connection"}],["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90","children":"Connected"}]]}],["$","div",null,{"className":"flex items-center justify-between","children":[["$","span",null,{"className":"text-sm","children":"Last Backup"}],["$","span",null,{"className":"text-sm text-muted-foreground","children":"2 hours ago"}]]}],["$","div",null,{"className":"flex items-center justify-between","children":[["$","span",null,{"className":"text-sm","children":"Active Users"}],["$","span",null,{"className":"text-sm text-muted-foreground","children":"12"}]]}]]}]}]]}]
1a:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
16:null
1e:I[8175,[],"IconMark"]
18:{"metadata":[["$","title","0",{"children":"QRSAMS - QR-Code Based Student Attendance and Monitoring System"}],["$","meta","1",{"name":"description","content":"Tanauan School of Arts and Trade - Student Attendance Management System"}],["$","link","2",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}],["$","$L1e","3",{}]],"error":null,"digest":"$undefined"}
1d:"$18:metadata"
