{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_da5a9c44._.js", "server/edge/chunks/node_modules_@auth_core_ec7fbf6d._.js", "server/edge/chunks/node_modules_jose_dist_webapi_49ff121e._.js", "server/edge/chunks/node_modules_eb9c5e08._.js", "server/edge/chunks/[root-of-the-server]__54fca45c._.js", "server/edge/chunks/edge-wrapper_4a07d54b.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "lNDM2CXPBhWttcW7WobUNEeeifmBiuxwGh+NkJdYoOg=", "__NEXT_PREVIEW_MODE_ID": "5829da3c7b5d81e400b4fd4f72fa1310", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "8d5977b27b34cf01526f9e230c26b2fdefb4cccc09434f31b1464695775ebaa3", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "92069db456d7b42bf03a8704c4e449956e3d99daff79a57dc3aaabf674e6eaea"}}}, "sortedMiddleware": ["/"], "functions": {}}