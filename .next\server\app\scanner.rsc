1:"$Sreact.fragment"
2:I[1321,["493","static/chunks/493-9183a606786a8e5a.js","177","static/chunks/app/layout-b91f0cb18d2a387d.js"],"ThemeProvider"]
3:I[7236,["493","static/chunks/493-9183a606786a8e5a.js","177","static/chunks/app/layout-b91f0cb18d2a387d.js"],"AuthProvider"]
4:I[7555,[],""]
5:I[1295,[],""]
c:I[8393,[],""]
:HL["/_next/static/css/9607e0db544c62ae.css","style"]
0:{"P":null,"b":"uMSIbYafsYeq_W1FcyE44","p":"","c":["","scanner"],"i":false,"f":[[["",{"children":["scanner",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/9607e0db544c62ae.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","suppressHydrationWarning":true,"children":["$","body",null,{"className":"__variable_5cfdac __variable_9a8899 antialiased","children":["$","$L2",null,{"attribute":"class","defaultTheme":"system","enableSystem":true,"disableTransitionOnChange":true,"children":["$","$L3",null,{"children":["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]}]}]}]]}],{"children":["scanner",["$","$1","c",{"children":[null,["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","div",null,{"className":"min-h-screen bg-background p-6","children":["$","div",null,{"className":"max-w-4xl mx-auto space-y-6","children":[["$","div",null,{"className":"text-center","children":[["$","h1",null,{"className":"text-3xl font-bold tracking-tight","children":"QR Code Scanner"}],["$","p",null,{"className":"text-muted-foreground","children":"Scan student QR codes for attendance tracking"}]]}],["$","div",null,{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6","children":["$","div",null,{"data-slot":"card-title","className":"leading-none font-semibold flex items-center gap-2","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-camera h-5 w-5","aria-hidden":"true","children":[["$","path","1tc9qg",{"d":"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z"}],["$","circle","1vg3eu",{"cx":"12","cy":"13","r":"3"}],"$undefined"]}],"Scanner Status"]}]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":["$","div",null,{"className":"flex items-center justify-between","children":[["$","div",null,{"className":"flex items-center gap-2","children":[["$","div",null,{"className":"h-3 w-3 bg-green-500 rounded-full animate-pulse"}],["$","span",null,{"className":"text-sm font-medium","children":"Camera Active"}]]}],["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90","children":"Ready to Scan"}]]}]}]]}],"$L6","$L7","$L8","$L9"]}]}],null,"$La"]}],{},null,false]},null,false]},null,false],"$Lb",false]],"m":"$undefined","G":["$c",[]],"s":false,"S":true}
13:I[9665,[],"OutletBoundary"]
15:I[4911,[],"AsyncMetadataOutlet"]
17:I[9665,[],"ViewportBoundary"]
19:I[9665,[],"MetadataBoundary"]
1a:"$Sreact.suspense"
6:["$","div",null,{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6","children":[["$","div",null,{"data-slot":"card-title","className":"leading-none font-semibold","children":"QR Code Scanner"}],["$","div",null,{"data-slot":"card-description","className":"text-muted-foreground text-sm","children":"Position the QR code within the frame to scan"}]]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":["$","div",null,{"className":"aspect-square max-w-md mx-auto bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center border-2 border-dashed border-gray-300 dark:border-gray-600","children":["$","div",null,{"className":"text-center","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-qr-code h-16 w-16 mx-auto mb-4 text-muted-foreground","aria-hidden":"true","children":[["$","rect","1tu5fj",{"width":"5","height":"5","x":"3","y":"3","rx":"1"}],["$","rect","1v8r4q",{"width":"5","height":"5","x":"16","y":"3","rx":"1"}],["$","rect","1x03jg",{"width":"5","height":"5","x":"3","y":"16","rx":"1"}],["$","path","177gqh",{"d":"M21 16h-3a2 2 0 0 0-2 2v3"}],["$","path","ents32",{"d":"M21 21v.01"}],["$","path","8crl2c",{"d":"M12 7v3a2 2 0 0 1-2 2H7"}],["$","path","nlz23k",{"d":"M3 12h.01"}],["$","path","n36tog",{"d":"M12 3h.01"}],["$","path","133mhm",{"d":"M12 16v.01"}],["$","path","1slzba",{"d":"M16 12h1"}],["$","path","1lwtk9",{"d":"M21 12v.01"}],["$","path","1880an",{"d":"M12 21v-1"}],"$undefined"]}],["$","p",null,{"className":"text-muted-foreground","children":"Camera feed will appear here"}],["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive bg-primary text-primary-foreground shadow-xs hover:bg-primary/90 h-9 px-4 py-2 has-[>svg]:px-3 mt-4","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-camera mr-2 h-4 w-4","aria-hidden":"true","children":[["$","path","1tc9qg",{"d":"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z"}],["$","circle","1vg3eu",{"cx":"12","cy":"13","r":"3"}],"$undefined"]}],"Start Camera"]}]]}]}]}]]}]
7:["$","div",null,{"className":"grid gap-4 md:grid-cols-3","children":[["$","div",null,{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header auto-rows-min grid-rows-[auto_auto] gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 flex flex-row items-center justify-between space-y-0 pb-2","children":[["$","div",null,{"data-slot":"card-title","className":"text-sm font-medium","children":"Scans Today"}],["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-qr-code h-4 w-4 text-muted-foreground","aria-hidden":"true","children":[["$","rect","1tu5fj",{"width":"5","height":"5","x":"3","y":"3","rx":"1"}],["$","rect","1v8r4q",{"width":"5","height":"5","x":"16","y":"3","rx":"1"}],["$","rect","1x03jg",{"width":"5","height":"5","x":"3","y":"16","rx":"1"}],["$","path","177gqh",{"d":"M21 16h-3a2 2 0 0 0-2 2v3"}],["$","path","ents32",{"d":"M21 21v.01"}],["$","path","8crl2c",{"d":"M12 7v3a2 2 0 0 1-2 2H7"}],["$","path","nlz23k",{"d":"M3 12h.01"}],["$","path","n36tog",{"d":"M12 3h.01"}],["$","path","133mhm",{"d":"M12 16v.01"}],["$","path","1slzba",{"d":"M16 12h1"}],["$","path","1lwtk9",{"d":"M21 12v.01"}],["$","path","1880an",{"d":"M12 21v-1"}],"$undefined"]}]]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":[["$","div",null,{"className":"text-2xl font-bold","children":"1,105"}],["$","p",null,{"className":"text-xs text-muted-foreground","children":"+23 in the last hour"}]]}]]}],["$","div",null,{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header auto-rows-min grid-rows-[auto_auto] gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 flex flex-row items-center justify-between space-y-0 pb-2","children":[["$","div",null,{"data-slot":"card-title","className":"text-sm font-medium","children":"Success Rate"}],["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-circle-check-big h-4 w-4 text-green-600","aria-hidden":"true","children":[["$","path","yps3ct",{"d":"M21.801 10A10 10 0 1 1 17 3.335"}],["$","path","1pflzl",{"d":"m9 11 3 3L22 4"}],"$undefined"]}]]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":[["$","div",null,{"className":"text-2xl font-bold","children":"98.5%"}],["$","p",null,{"className":"text-xs text-muted-foreground","children":"1,089 successful scans"}]]}]]}],["$","div",null,{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header auto-rows-min grid-rows-[auto_auto] gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 flex flex-row items-center justify-between space-y-0 pb-2","children":[["$","div",null,{"data-slot":"card-title","className":"text-sm font-medium","children":"Failed Scans"}],["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-circle-x h-4 w-4 text-red-600","aria-hidden":"true","children":[["$","circle","1mglay",{"cx":"12","cy":"12","r":"10"}],["$","path","1uzhvr",{"d":"m15 9-6 6"}],["$","path","z0biqf",{"d":"m9 9 6 6"}],"$undefined"]}]]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":[["$","div",null,{"className":"text-2xl font-bold","children":"16"}],["$","p",null,{"className":"text-xs text-muted-foreground","children":"Invalid or damaged QR codes"}]]}]]}]]}]
8:["$","div",null,{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6","children":[["$","div",null,{"data-slot":"card-title","className":"leading-none font-semibold","children":"Recent Scans"}],["$","div",null,{"data-slot":"card-description","className":"text-muted-foreground text-sm","children":"Latest QR code scan results"}]]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":["$","div",null,{"className":"space-y-4","children":[["$","div","SCAN001",{"className":"flex items-center justify-between p-3 border rounded-lg","children":[["$","div",null,{"className":"flex items-center gap-3","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-circle-check-big h-5 w-5 text-green-600","aria-hidden":"true","children":[["$","path","yps3ct",{"d":"M21.801 10A10 10 0 1 1 17 3.335"}],["$","path","1pflzl",{"d":"m9 11 3 3L22 4"}],"$undefined"]}],false,false,["$","div",null,{"children":[["$","p",null,{"className":"font-medium","children":"John Doe"}],["$","p",null,{"className":"text-sm text-muted-foreground","children":["ID: ","STU001"]}]]}]]}],["$","div",null,{"className":"text-right","children":[["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90","children":"Check In"}],["$","p",null,{"className":"text-sm text-muted-foreground mt-1","children":"8:30 AM"}]]}]]}],["$","div","SCAN002",{"className":"flex items-center justify-between p-3 border rounded-lg","children":[["$","div",null,{"className":"flex items-center gap-3","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-circle-check-big h-5 w-5 text-green-600","aria-hidden":"true","children":[["$","path","yps3ct",{"d":"M21.801 10A10 10 0 1 1 17 3.335"}],["$","path","1pflzl",{"d":"m9 11 3 3L22 4"}],"$undefined"]}],false,false,["$","div",null,{"children":[["$","p",null,{"className":"font-medium","children":"Jane Smith"}],["$","p",null,{"className":"text-sm text-muted-foreground","children":["ID: ","STU002"]}]]}]]}],["$","div",null,{"className":"text-right","children":[["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90","children":"Check In"}],["$","p",null,{"className":"text-sm text-muted-foreground mt-1","children":"8:25 AM"}]]}]]}],["$","div","SCAN003",{"className":"flex items-center justify-between p-3 border rounded-lg","children":[["$","div",null,{"className":"flex items-center gap-3","children":[false,["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-circle-x h-5 w-5 text-red-600","aria-hidden":"true","children":["$Ld","$Le","$Lf","$undefined"]}],false,"$L10"]}],"$L11"]}],"$L12"]}]}]]}]
9:["$","div",null,{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6","children":[["$","div",null,{"data-slot":"card-title","className":"leading-none font-semibold","children":"Scanner Controls"}],["$","div",null,{"data-slot":"card-description","className":"text-muted-foreground text-sm","children":"Manage scanner settings and operations"}]]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":["$","div",null,{"className":"grid gap-4 md:grid-cols-2","children":[["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 px-4 py-2 has-[>svg]:px-3 h-16","children":["$","div",null,{"className":"text-center","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-camera h-6 w-6 mx-auto mb-1","aria-hidden":"true","children":[["$","path","1tc9qg",{"d":"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z"}],["$","circle","1vg3eu",{"cx":"12","cy":"13","r":"3"}],"$undefined"]}],["$","span",null,{"className":"text-sm","children":"Switch Camera"}]]}]}],["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 px-4 py-2 has-[>svg]:px-3 h-16","children":["$","div",null,{"className":"text-center","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-qr-code h-6 w-6 mx-auto mb-1","aria-hidden":"true","children":[["$","rect","1tu5fj",{"width":"5","height":"5","x":"3","y":"3","rx":"1"}],["$","rect","1v8r4q",{"width":"5","height":"5","x":"16","y":"3","rx":"1"}],["$","rect","1x03jg",{"width":"5","height":"5","x":"3","y":"16","rx":"1"}],["$","path","177gqh",{"d":"M21 16h-3a2 2 0 0 0-2 2v3"}],["$","path","ents32",{"d":"M21 21v.01"}],["$","path","8crl2c",{"d":"M12 7v3a2 2 0 0 1-2 2H7"}],["$","path","nlz23k",{"d":"M3 12h.01"}],["$","path","n36tog",{"d":"M12 3h.01"}],["$","path","133mhm",{"d":"M12 16v.01"}],["$","path","1slzba",{"d":"M16 12h1"}],["$","path","1lwtk9",{"d":"M21 12v.01"}],["$","path","1880an",{"d":"M12 21v-1"}],"$undefined"]}],["$","span",null,{"className":"text-sm","children":"Manual Entry"}]]}]}]]}]}]]}]
a:["$","$L13",null,{"children":["$L14",["$","$L15",null,{"promise":"$@16"}]]}]
b:["$","$1","h",{"children":[null,[["$","$L17",null,{"children":"$L18"}],null],["$","$L19",null,{"children":["$","div",null,{"hidden":true,"children":["$","$1a",null,{"fallback":null,"children":"$L1b"}]}]}]]}]
d:["$","circle","1mglay",{"cx":"12","cy":"12","r":"10"}]
e:["$","path","1uzhvr",{"d":"m15 9-6 6"}]
f:["$","path","z0biqf",{"d":"m9 9 6 6"}]
10:["$","div",null,{"children":[["$","p",null,{"className":"font-medium","children":"Unknown Student"}],["$","p",null,{"className":"text-sm text-muted-foreground","children":["ID: ","STU999"]}]]}]
11:["$","div",null,{"className":"text-right","children":[["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60","children":"Invalid QR"}],["$","p",null,{"className":"text-sm text-muted-foreground mt-1","children":"8:45 AM"}]]}]
12:["$","div","SCAN004",{"className":"flex items-center justify-between p-3 border rounded-lg","children":[["$","div",null,{"className":"flex items-center gap-3","children":[false,false,["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-clock h-5 w-5 text-yellow-600","aria-hidden":"true","children":[["$","path","mmk7yg",{"d":"M12 6v6l4 2"}],["$","circle","1mglay",{"cx":"12","cy":"12","r":"10"}],"$undefined"]}],["$","div",null,{"children":[["$","p",null,{"className":"font-medium","children":"Mike Johnson"}],["$","p",null,{"className":"text-sm text-muted-foreground","children":["ID: ","STU003"]}]]}]]}],["$","div",null,{"className":"text-right","children":[["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90","children":"Late Check In"}],["$","p",null,{"className":"text-sm text-muted-foreground mt-1","children":"8:45 AM"}]]}]]}]
18:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
14:null
1c:I[8175,[],"IconMark"]
16:{"metadata":[["$","title","0",{"children":"QRSAMS - QR-Code Based Student Attendance and Monitoring System"}],["$","meta","1",{"name":"description","content":"Tanauan School of Arts and Trade - Student Attendance Management System"}],["$","link","2",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}],["$","$L1c","3",{}]],"error":null,"digest":"$undefined"}
1b:"$16:metadata"
