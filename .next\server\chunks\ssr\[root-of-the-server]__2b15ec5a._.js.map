{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 22, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 131, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 175, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/app/%28dashboard%29/dashboard/page.tsx"], "sourcesContent": ["import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from \"@/components/ui/card\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Users, ClipboardCheck, TrendingUp, Calendar } from \"lucide-react\"\n\nexport default function DashboardPage() {\n  return (\n    <div className=\"space-y-6\">\n      <div>\n        <h1 className=\"text-3xl font-bold tracking-tight\">Dashboard</h1>\n        <p className=\"text-muted-foreground\">\n          Welcome to QRSAMS - QR-Code Based Student Attendance and Monitoring System\n        </p>\n      </div>\n\n      {/* Stats Cards */}\n      <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-4\">\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Total Students</CardTitle>\n            <Users className=\"h-4 w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">1,234</div>\n            <p className=\"text-xs text-muted-foreground\">\n              +20 from last month\n            </p>\n          </CardContent>\n        </Card>\n        \n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Today&apos;s Attendance</CardTitle>\n            <ClipboardCheck className=\"h-4 w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">89.5%</div>\n            <p className=\"text-xs text-muted-foreground\">\n              +2.1% from yesterday\n            </p>\n          </CardContent>\n        </Card>\n        \n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Weekly Average</CardTitle>\n            <TrendingUp className=\"h-4 w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">92.3%</div>\n            <p className=\"text-xs text-muted-foreground\">\n              +5.2% from last week\n            </p>\n          </CardContent>\n        </Card>\n        \n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Active Classes</CardTitle>\n            <Calendar className=\"h-4 w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">24</div>\n            <p className=\"text-xs text-muted-foreground\">\n              8 ongoing sessions\n            </p>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Recent Activity */}\n      <div className=\"grid gap-4 md:grid-cols-2\">\n        <Card>\n          <CardHeader>\n            <CardTitle>Recent Attendance</CardTitle>\n            <CardDescription>Latest student check-ins</CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-4\">\n              {[\n                { name: \"John Doe\", time: \"8:30 AM\", status: \"present\" },\n                { name: \"Jane Smith\", time: \"8:25 AM\", status: \"present\" },\n                { name: \"Mike Johnson\", time: \"8:45 AM\", status: \"late\" },\n                { name: \"Sarah Wilson\", time: \"8:20 AM\", status: \"present\" },\n              ].map((student, index) => (\n                <div key={index} className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-sm font-medium\">{student.name}</p>\n                    <p className=\"text-xs text-muted-foreground\">{student.time}</p>\n                  </div>\n                  <Badge variant={student.status === \"present\" ? \"default\" : \"secondary\"}>\n                    {student.status}\n                  </Badge>\n                </div>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader>\n            <CardTitle>System Status</CardTitle>\n            <CardDescription>Current system information</CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm\">QR Scanner Status</span>\n                <Badge variant=\"default\">Online</Badge>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm\">Database Connection</span>\n                <Badge variant=\"default\">Connected</Badge>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm\">Last Backup</span>\n                <span className=\"text-sm text-muted-foreground\">2 hours ago</span>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm\">Active Users</span>\n                <span className=\"text-sm text-muted-foreground\">12</span>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAAoC;;;;;;kCAClD,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAMvC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,yHAAA,CAAA,OAAI;;0CACH,8OAAC,yHAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,yHAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;0CAEnB,8OAAC,yHAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAI,WAAU;kDAAqB;;;;;;kDACpC,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;kCAMjD,8OAAC,yHAAA,CAAA,OAAI;;0CACH,8OAAC,yHAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,yHAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,8OAAC,0NAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;;;;;;;0CAE5B,8OAAC,yHAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAI,WAAU;kDAAqB;;;;;;kDACpC,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;kCAMjD,8OAAC,yHAAA,CAAA,OAAI;;0CACH,8OAAC,yHAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,yHAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;0CAExB,8OAAC,yHAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAI,WAAU;kDAAqB;;;;;;kDACpC,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;kCAMjD,8OAAC,yHAAA,CAAA,OAAI;;0CACH,8OAAC,yHAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,yHAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;0CAEtB,8OAAC,yHAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAI,WAAU;kDAAqB;;;;;;kDACpC,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;;;;;;;0BAQnD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,yHAAA,CAAA,OAAI;;0CACH,8OAAC,yHAAA,CAAA,aAAU;;kDACT,8OAAC,yHAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,yHAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAEnB,8OAAC,yHAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CACZ;wCACC;4CAAE,MAAM;4CAAY,MAAM;4CAAW,QAAQ;wCAAU;wCACvD;4CAAE,MAAM;4CAAc,MAAM;4CAAW,QAAQ;wCAAU;wCACzD;4CAAE,MAAM;4CAAgB,MAAM;4CAAW,QAAQ;wCAAO;wCACxD;4CAAE,MAAM;4CAAgB,MAAM;4CAAW,QAAQ;wCAAU;qCAC5D,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,8OAAC;4CAAgB,WAAU;;8DACzB,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAAuB,QAAQ,IAAI;;;;;;sEAChD,8OAAC;4DAAE,WAAU;sEAAiC,QAAQ,IAAI;;;;;;;;;;;;8DAE5D,8OAAC,0HAAA,CAAA,QAAK;oDAAC,SAAS,QAAQ,MAAM,KAAK,YAAY,YAAY;8DACxD,QAAQ,MAAM;;;;;;;2CANT;;;;;;;;;;;;;;;;;;;;;kCAclB,8OAAC,yHAAA,CAAA,OAAI;;0CACH,8OAAC,yHAAA,CAAA,aAAU;;kDACT,8OAAC,yHAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,yHAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAEnB,8OAAC,yHAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC,0HAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;;;;;;;sDAE3B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC,0HAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;;;;;;;sDAE3B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAK,WAAU;8DAAgC;;;;;;;;;;;;sDAElD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAK,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhE", "debugId": null}}]}